﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="source\hls-media.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-m3u8.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-fmp4.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-parser.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-playlist.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-master.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\hls-string.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\hls-param.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\hls-media.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\hls-m3u8.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\hls-h264.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="include\hls-fmp4.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\hls-parser.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\hls-string.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>