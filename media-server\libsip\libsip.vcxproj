﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\sip-agent.h" />
    <ClInclude Include="include\sip-dialog.h" />
    <ClInclude Include="include\sip-header.h" />
    <ClInclude Include="include\sip-message.h" />
    <ClInclude Include="include\sip-timer.h" />
    <ClInclude Include="include\sip-transport.h" />
    <ClInclude Include="include\sip-uac.h" />
    <ClInclude Include="include\sip-uas.h" />
    <ClInclude Include="include\sip-subscribe.h" />
    <ClInclude Include="src\sip-internal.h" />
    <ClInclude Include="src\uac\sip-uac-transaction.h" />
    <ClInclude Include="src\uas\sip-uas-transaction.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="src\header\sip-header-contact.c" />
    <ClCompile Include="src\header\sip-header-param.c" />
    <ClCompile Include="src\header\sip-header-cseq.c" />
    <ClCompile Include="src\header\sip-header-route.c" />
    <ClCompile Include="src\header\sip-header-substate.c" />
    <ClCompile Include="src\header\sip-header-uri.c" />
    <ClCompile Include="src\header\sip-header-via.c" />
    <ClCompile Include="src\header\sip-header.c" />
    <ClCompile Include="src\sip-agent.c" />
    <ClCompile Include="src\sip-dialog.c" />
    <ClCompile Include="src\sip-message.c" />
    <ClCompile Include="src\sip-reason.c" />
    <ClCompile Include="src\sip-subscribe.c" />
    <ClCompile Include="src\uac\sip-uac-ack.c" />
    <ClCompile Include="src\uac\sip-uac-bye.c" />
    <ClCompile Include="src\uac\sip-uac-cancel.c" />
    <ClCompile Include="src\uac\sip-uac-info.c" />
    <ClCompile Include="src\uac\sip-uac-invite.c" />
    <ClCompile Include="src\uac\sip-uac-options.c" />
    <ClCompile Include="src\uac\sip-uac-register.c" />
    <ClCompile Include="src\uac\sip-uac-subscribe.c" />
    <ClCompile Include="src\uac\sip-uac-transaction-invite.c" />
    <ClCompile Include="src\uac\sip-uac-transaction-noninvite.c" />
    <ClCompile Include="src\uac\sip-uac-transaction.c" />
    <ClCompile Include="src\uac\sip-uac.c" />
    <ClCompile Include="src\uas\sip-uas-bye.c" />
    <ClCompile Include="src\uas\sip-uas-cancel.c" />
    <ClCompile Include="src\uas\sip-uas-info.c" />
    <ClCompile Include="src\uas\sip-uas-options.c" />
    <ClCompile Include="src\uas\sip-uas-prack.c" />
    <ClCompile Include="src\uas\sip-uas-refer.c" />
    <ClCompile Include="src\uas\sip-uas-register.c" />
    <ClCompile Include="src\uas\sip-uas-subscribe.c" />
    <ClCompile Include="src\uas\sip-uas-transaction-invite.c" />
    <ClCompile Include="src\uas\sip-uas-transaction-noninvite.c" />
    <ClCompile Include="src\uas\sip-uas-transaction.c" />
    <ClCompile Include="src\uas\sip-uas.c" />
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{5CDF2275-37A7-450A-B69F-2063079D6946}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <RootNamespace>libsip</RootNamespace>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <PlatformToolset>v143</PlatformToolset>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="Shared">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
    <RunCodeAnalysis>true</RunCodeAnalysis>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>OS_WINDOWS;WIN32;_DEBUG;_LIB;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;include;src;../../sdk/include;../../sdk/libhttp/include</AdditionalIncludeDirectories>
      <EnablePREfast>true</EnablePREfast>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>OS_WINDOWS;_DEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;include;src;../../sdk/include;../../sdk/libhttp/include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>OS_WINDOWS;WIN32;NDEBUG;_LIB;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;include;src;../../sdk/include;../../sdk/libhttp/include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>OS_WINDOWS;NDEBUG;_LIB;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>.;include;src;../../sdk/include;../../sdk/libhttp/include</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
    </Link>
  </ItemDefinitionGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>