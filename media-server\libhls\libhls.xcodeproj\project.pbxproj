// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		46C5B2F32183EDDB00419E57 /* list.h in Headers */ = {isa = PBXBuildFile; fileRef = 46C5B2EE2183EDDB00419E57 /* list.h */; };
		46C5B2F42183EDDB00419E57 /* hls-h264.h in Headers */ = {isa = PBXBuildFile; fileRef = 46C5B2EF2183EDDB00419E57 /* hls-h264.h */; };
		46C5B2F52183EDDB00419E57 /* hls-fmp4.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2F02183EDDB00419E57 /* hls-fmp4.c */; };
		46C5B2F62183EDDB00419E57 /* hls-m3u8.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2F12183EDDB00419E57 /* hls-m3u8.c */; };
		46C5B2F72183EDDB00419E57 /* hls-media.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2F22183EDDB00419E57 /* hls-media.c */; };
		46E55E4D24681D8400D8BDBA /* hls-parser.c in Sources */ = {isa = PBXBuildFile; fileRef = 46E55E4C24681D8400D8BDBA /* hls-parser.c */; };
		46E55E5C247384DE00D8BDBA /* hls-master.c in Sources */ = {isa = PBXBuildFile; fileRef = 46E55E5A247384DE00D8BDBA /* hls-master.c */; };
		46E55E5D247384DF00D8BDBA /* hls-playlist.c in Sources */ = {isa = PBXBuildFile; fileRef = 46E55E5B247384DE00D8BDBA /* hls-playlist.c */; };
		46E55E6124A7393F00D8BDBA /* hls-string.c in Sources */ = {isa = PBXBuildFile; fileRef = 46E55E6024A7393F00D8BDBA /* hls-string.c */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		46C5B2422183EB2C00419E57 /* libhls.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libhls.a; sourceTree = BUILT_PRODUCTS_DIR; };
		46C5B2EC2183EDD000419E57 /* include */ = {isa = PBXFileReference; lastKnownFileType = folder; path = include; sourceTree = "<group>"; };
		46C5B2EE2183EDDB00419E57 /* list.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = list.h; sourceTree = "<group>"; };
		46C5B2EF2183EDDB00419E57 /* hls-h264.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "hls-h264.h"; sourceTree = "<group>"; };
		46C5B2F02183EDDB00419E57 /* hls-fmp4.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-fmp4.c"; sourceTree = "<group>"; };
		46C5B2F12183EDDB00419E57 /* hls-m3u8.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-m3u8.c"; sourceTree = "<group>"; };
		46C5B2F22183EDDB00419E57 /* hls-media.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-media.c"; sourceTree = "<group>"; };
		46E55E4C24681D8400D8BDBA /* hls-parser.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-parser.c"; sourceTree = "<group>"; };
		46E55E5A247384DE00D8BDBA /* hls-master.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-master.c"; sourceTree = "<group>"; };
		46E55E5B247384DE00D8BDBA /* hls-playlist.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-playlist.c"; sourceTree = "<group>"; };
		46E55E6024A7393F00D8BDBA /* hls-string.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hls-string.c"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		46C5B2402183EB2C00419E57 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		46C5B2392183EB2C00419E57 = {
			isa = PBXGroup;
			children = (
				46C5B2ED2183EDDB00419E57 /* source */,
				46C5B2EC2183EDD000419E57 /* include */,
				46C5B2432183EB2C00419E57 /* Products */,
			);
			sourceTree = "<group>";
		};
		46C5B2432183EB2C00419E57 /* Products */ = {
			isa = PBXGroup;
			children = (
				46C5B2422183EB2C00419E57 /* libhls.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		46C5B2ED2183EDDB00419E57 /* source */ = {
			isa = PBXGroup;
			children = (
				46E55E6024A7393F00D8BDBA /* hls-string.c */,
				46E55E5A247384DE00D8BDBA /* hls-master.c */,
				46E55E5B247384DE00D8BDBA /* hls-playlist.c */,
				46C5B2EE2183EDDB00419E57 /* list.h */,
				46C5B2EF2183EDDB00419E57 /* hls-h264.h */,
				46C5B2F02183EDDB00419E57 /* hls-fmp4.c */,
				46C5B2F12183EDDB00419E57 /* hls-m3u8.c */,
				46C5B2F22183EDDB00419E57 /* hls-media.c */,
				46E55E4C24681D8400D8BDBA /* hls-parser.c */,
			);
			path = source;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		46C5B23E2183EB2C00419E57 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46C5B2F42183EDDB00419E57 /* hls-h264.h in Headers */,
				46C5B2F32183EDDB00419E57 /* list.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		46C5B2412183EB2C00419E57 /* libhls */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46C5B2462183EB2C00419E57 /* Build configuration list for PBXNativeTarget "libhls" */;
			buildPhases = (
				46C5B23E2183EB2C00419E57 /* Headers */,
				46C5B23F2183EB2C00419E57 /* Sources */,
				46C5B2402183EB2C00419E57 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = libhls;
			productName = libhls;
			productReference = 46C5B2422183EB2C00419E57 /* libhls.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		46C5B23A2183EB2C00419E57 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				ORGANIZATIONNAME = ireader;
				TargetAttributes = {
					46C5B2412183EB2C00419E57 = {
						CreatedOnToolsVersion = 10.0;
					};
				};
			};
			buildConfigurationList = 46C5B23D2183EB2C00419E57 /* Build configuration list for PBXProject "libhls" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 46C5B2392183EB2C00419E57;
			productRefGroup = 46C5B2432183EB2C00419E57 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				46C5B2412183EB2C00419E57 /* libhls */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		46C5B23F2183EB2C00419E57 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46C5B2F62183EDDB00419E57 /* hls-m3u8.c in Sources */,
				46C5B2F52183EDDB00419E57 /* hls-fmp4.c in Sources */,
				46E55E4D24681D8400D8BDBA /* hls-parser.c in Sources */,
				46E55E5D247384DF00D8BDBA /* hls-playlist.c in Sources */,
				46C5B2F72183EDDB00419E57 /* hls-media.c in Sources */,
				46E55E5C247384DE00D8BDBA /* hls-master.c in Sources */,
				46E55E6124A7393F00D8BDBA /* hls-string.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		46C5B2442183EB2C00419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					OS_MAC,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../libmov/include,
					../libmpeg/include,
					../../sdk/include,
				);
			};
			name = Debug;
		};
		46C5B2452183EB2C00419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = OS_MAC;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../libmov/include,
					../libmpeg/include,
					../../sdk/include,
				);
			};
			name = Release;
		};
		46C5B2472183EB2C00419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Debug;
		};
		46C5B2482183EB2C00419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		46C5B23D2183EB2C00419E57 /* Build configuration list for PBXProject "libhls" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B2442183EB2C00419E57 /* Debug */,
				46C5B2452183EB2C00419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46C5B2462183EB2C00419E57 /* Build configuration list for PBXNativeTarget "libhls" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B2472183EB2C00419E57 /* Debug */,
				46C5B2482183EB2C00419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 46C5B23A2183EB2C00419E57 /* Project object */;
}
