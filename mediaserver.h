#ifndef MEDIA_SERVER_H
#define MEDIA_SERVER_H

#include <memory>
#include <thread>
#include <vector>
#include <atomic>

// Forward declarations
class RtmpServer;
class RtspServer;
class HlsServer;
class HttpFlvServer;
class MediaSourceManager;

struct MediaServerConfig {
    int rtmp_port = 1935;
    int rtsp_port = 554;
    int http_port = 8080;
    int worker_threads = 4;
    std::string media_root = "./media";
};

class MediaServer {
public:
    MediaServer(std::shared_ptr<MediaSourceManager> sourceManager);
    ~MediaServer();

    bool Start(const MediaServerConfig& config);
    void Stop();
    bool IsRunning() const { return m_running; }

private:
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::unique_ptr<RtmpServer> m_rtmpServer;
    std::unique_ptr<RtspServer> m_rtspServer;
    std::unique_ptr<HlsServer> m_hlsServer;
    std::unique_ptr<HttpFlvServer> m_httpFlvServer;
    
    std::vector<std::thread> m_workers;
    std::atomic<bool> m_running{false};
    MediaServerConfig m_config;
    
    void WorkerThread();
};

#endif // MEDIA_SERVER_H