# MP4录像器 - 基于media-server的安防录像解决方案

基于media-server库开发的高性能MP4录像器，专门用于安防监控系统。支持从安防芯片SDK获取H.264和AAC数据，具备文件大小控制、时长控制和hash标签化快速查找功能。

## 功能特性

### 核心功能
- **多协议支持**: 支持从各种安防芯片SDK获取流媒体数据
- **MP4录制**: 高效的MP4文件录制，支持H.264视频和AAC音频
- **智能分段**: 支持按文件大小和录像时长自动分段
- **Hash标签化**: 自动计算MD5和SHA256哈希值，支持快速查找和去重
- **多任务管理**: 支持同时管理多个录像任务

### 厂商SDK支持
- **海康威视** (Hikvision)
- **大华** (Dahua) 
- **宇视** (Uniview)
- **通用接口** (Generic)

### 高级特性
- **存储管理**: 智能存储空间监控和自动清理
- **事件触发**: 支持移动侦测和报警触发录像
- **时间调度**: 支持按时间段和星期几的录像调度
- **标签系统**: 灵活的标签系统，支持按标签快速搜索
- **统计分析**: 详细的录像统计和分析功能

## 系统要求

### 编译环境
- C++17或更高版本
- CMake 3.10+
- GCC 7.0+ 或 Clang 6.0+

### 依赖库
- [media-server](https://github.com/ireader/media-server) - 流媒体处理库
- [sdk](https://github.com/ireader/sdk) - 网络I/O库
- OpenSSL - 加密和哈希计算
- JsonCpp - JSON配置文件处理
- pthread - 多线程支持

## 编译安装

### 1. 系统依赖安装

#### Ubuntu/Debian
```bash
sudo apt-get update
sudo apt-get install build-essential cmake git
sudo apt-get install libssl-dev libjsoncpp-dev
sudo apt-get install pkg-config
```

#### CentOS/RHEL
```bash
sudo yum groupinstall "Development Tools"
sudo yum install cmake git openssl-devel jsoncpp-devel
sudo yum install pkg-config
```

### 2. 下载依赖库

```bash
# 克隆主项目
git clone <your-repo-url> mp4-recorder
cd mp4-recorder

# 下载media-server库
git clone https://github.com/ireader/media-server.git

# 下载sdk库
cd ..
git clone https://github.com/ireader/sdk.git
```

### 3. 编译依赖库

```bash
# 编译SDK
cd sdk
make

# 编译media-server
cd ../media-server
make

# 验证编译结果
ls -la libmov/libmov.a libflv/libflv.a librtmp/librtmp.a
```

### 4. 编译MP4录像器

#### 使用CMake（推荐）
```bash
cd ../mp4-recorder
mkdir build
cd build
cmake ..
make -j$(nproc)
make install
```

#### 使用Make
```bash
cd ../mp4-recorder
make -f Makefile.mp4recorder all
```

### 5. 验证安装

```bash
# 运行测试程序
./bin/mp4-recorder-test

# 运行示例程序
./bin/mp4-recorder-example
```

## 使用方法

### 快速开始

```bash
# 1. 创建配置文件
cp config/recording_config.json.example config/recording_config.json

# 2. 编辑配置文件，设置设备IP和认证信息
vim config/recording_config.json

# 3. 运行示例程序
./bin/mp4-recorder-example

# 4. 运行完整示例
./bin/complete-example
```

### 基本编程使用

```cpp
#include "recording-manager.h"

int main() {
    // 创建录像管理器
    RecordingManager manager;

    // 创建录像任务
    RecordingTask task;
    task.task_id = "camera_001";
    task.device_id = "camera_001";
    task.sdk_type = SecurityChipSDKFactory::HIKVISION;

    // 设备配置
    task.chip_config.ip_address = "*************";
    task.chip_config.username = "admin";
    task.chip_config.password = "123456";
    task.chip_config.video_channel = 0;
    task.chip_config.enable_audio = true;

    // 录像配置
    task.recording_config.output_dir = "./recordings";
    task.recording_config.max_file_size = 100 * 1024 * 1024; // 100MB
    task.recording_config.max_duration = 300; // 5分钟
    task.recording_config.enable_hash_tagging = true;

    // 添加标签
    task.tags["location"] = "Building_A";
    task.tags["camera_type"] = "Dome";

    // 设置回调
    manager.SetRecordingCompleteCallback([](const std::string& task_id, const RecordingInfo& info) {
        std::cout << "Recording completed: " << info.filename << std::endl;
    });

    // 启动录像
    manager.Start();
    manager.AddTask(task);
    manager.StartTask("camera_001");

    // 等待录像
    std::this_thread::sleep_for(std::chrono::minutes(10));

    // 停止录像
    manager.StopTask("camera_001");
    manager.Stop();

    return 0;
}
```

### 高级功能使用

```cpp
// 1. 批量管理多个设备
std::vector<RecordingTask> tasks = {
    CreateHikvisionTask("*************"),
    CreateDahuaTask("192.168.1.101"),
    CreateUnivviewTask("192.168.1.102")
};

for (const auto& task : tasks) {
    manager.AddTask(task);
}
manager.StartAllTasks();

// 2. 按条件搜索录像
auto recent_recordings = manager.GetRecordingsByTimeRange(
    std::chrono::system_clock::now() - std::chrono::hours(24),
    std::chrono::system_clock::now()
);

auto building_a_recordings = manager.SearchRecordings("Building_A");

// 3. 存储管理
uint64_t total_used = manager.GetTotalStorageUsed();
manager.CleanupOldRecordings(30); // 保留30天
manager.CleanupByStorageLimit(100 * 1024 * 1024 * 1024ULL); // 限制100GB

// 4. 配置保存和加载
manager.SaveConfiguration("./config/my_config.json");
manager.LoadConfiguration("./config/my_config.json");
```

### 配置文件使用

```bash
# 复制配置文件模板
cp config/recording_config.json.example config/recording_config.json

# 编辑配置文件
vim config/recording_config.json

# 使用配置文件启动
./mp4-recorder-example --config config/recording_config.json
```

### 运行示例程序

```bash
# 运行示例程序
./mp4-recorder-example

# 运行测试程序
./mp4-recorder-test
```

## 配置说明

### 录像任务配置

```json
{
  "task_id": "camera_001",
  "device_id": "camera_001",
  "continuous_recording": true,
  "motion_detection": false,
  "alarm_trigger": false,
  "chip_config": {
    "ip_address": "*************",
    "port": 8000,
    "username": "admin",
    "password": "123456",
    "video_channel": 0,
    "enable_audio": true
  },
  "recording_config": {
    "output_dir": "./recordings/camera_001",
    "max_file_size": 104857600,
    "max_duration": 300,
    "enable_hash_tagging": true,
    "file_prefix": "cam001_record"
  },
  "tags": {
    "location": "Building_A_Floor_1",
    "camera_type": "Dome",
    "resolution": "1080P"
  }
}
```

### 存储管理配置

```json
{
  "storage_warning_threshold": 85899345920,
  "cleanup_interval_hours": 24
}
```

## API文档

### MP4Recorder类

```cpp
class MP4Recorder {
public:
    // 启动/停止录像
    bool StartRecording(const std::string& session_id = "");
    bool StopRecording();
    
    // 媒体数据输入
    bool WriteVideoFrame(const MediaFrame& frame);
    bool WriteAudioFrame(const MediaFrame& frame);
    
    // 获取录像信息
    std::vector<RecordingInfo> GetAllRecordings() const;
    bool FindRecordingByHash(const std::string& hash, RecordingInfo& info) const;
};
```

### RecordingManager类

```cpp
class RecordingManager {
public:
    // 任务管理
    bool AddTask(const RecordingTask& task);
    bool StartTask(const std::string& task_id);
    bool StopTask(const std::string& task_id);
    
    // 录像文件管理
    std::vector<RecordingInfo> GetAllRecordings() const;
    std::vector<RecordingInfo> SearchRecordings(const std::string& query) const;
    
    // 存储管理
    bool CleanupOldRecordings(uint32_t days_to_keep);
    bool CleanupByStorageLimit(uint64_t max_storage_bytes);
};
```

## 性能优化

- **多线程处理**: 使用独立线程处理录像任务，避免阻塞
- **内存池**: 优化内存分配，减少碎片
- **异步I/O**: 使用异步文件写入，提高性能
- **智能缓存**: GOP缓存机制，优化关键帧处理
- **零拷贝**: 减少不必要的内存拷贝操作

## 故障排查

### 常见问题

1. **编译错误**
   - 检查依赖库是否正确安装
   - 确认C++17支持
   - 检查CMake版本

2. **连接设备失败**
   - 检查网络连接
   - 确认设备IP地址和端口
   - 验证用户名密码

3. **录像文件损坏**
   - 检查磁盘空间
   - 确认文件权限
   - 查看错误日志

### 调试模式

```bash
# 编译调试版本
cmake -DCMAKE_BUILD_TYPE=Debug ..
make

# 运行调试版本
gdb ./mp4-recorder-example
```

## 许可证

本项目基于MIT许可证开源。

## 贡献指南

欢迎提交Issue和Pull Request。

## 联系方式

- 项目主页: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 技术支持: [Email]
