#ifndef MP4_RECORDER_H
#define MP4_RECORDER_H

#include <string>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <queue>
#include <functional>
#include <chrono>
#include <map>
#include <vector>

// media-server includes
extern "C" {
#include "mp4-writer.h"
#include "mov-format.h"
#include "mov-buffer.h"
}

// 媒体数据包结构
struct MediaFrame {
    enum Type {
        VIDEO_H264 = 0,
        VIDEO_H265 = 1,
        AUDIO_AAC = 2,
        AUDIO_G711A = 3,
        AUDIO_G711U = 4
    };
    
    Type type;
    std::vector<uint8_t> data;
    int64_t pts;        // 显示时间戳 (ms)
    int64_t dts;        // 解码时间戳 (ms)
    bool keyframe;      // 是否关键帧
    uint32_t track_id;  // 轨道ID
};

// 录像配置
struct RecordingConfig {
    std::string output_dir = "./recordings";    // 输出目录
    uint64_t max_file_size = 100 * 1024 * 1024; // 最大文件大小 (100MB)
    uint32_t max_duration = 300;                 // 最大录像时长 (秒)
    bool enable_hash_tagging = true;             // 启用hash标签
    std::string file_prefix = "record";          // 文件名前缀
    
    // 视频参数
    int video_width = 1920;
    int video_height = 1080;
    int video_fps = 25;
    
    // 音频参数
    int audio_channels = 2;
    int audio_sample_rate = 48000;
    int audio_bits_per_sample = 16;
};

// 录像文件信息
struct RecordingInfo {
    std::string filename;
    std::string filepath;
    std::string hash_md5;
    std::string hash_sha256;
    uint64_t file_size;
    uint32_t duration;
    std::chrono::system_clock::time_point start_time;
    std::chrono::system_clock::time_point end_time;
    std::map<std::string, std::string> tags;
};

// 录像状态
enum class RecordingState {
    STOPPED,
    STARTING,
    RECORDING,
    STOPPING,
    ERROR
};

// MP4录像器类
class MP4Recorder {
public:
    MP4Recorder(const RecordingConfig& config);
    ~MP4Recorder();
    
    // 启动/停止录像
    bool StartRecording(const std::string& session_id = "");
    bool StopRecording();
    bool IsRecording() const { return m_state == RecordingState::RECORDING; }
    RecordingState GetState() const { return m_state; }
    
    // 媒体数据输入
    bool WriteVideoFrame(const MediaFrame& frame);
    bool WriteAudioFrame(const MediaFrame& frame);
    
    // 设置视频/音频参数
    bool SetVideoParams(int width, int height, int fps, const std::vector<uint8_t>& sps, const std::vector<uint8_t>& pps);
    bool SetAudioParams(int channels, int sample_rate, int bits_per_sample, const std::vector<uint8_t>& config);

    // H.264参数集处理
    bool SetH264ExtraData(const std::vector<uint8_t>& sps, const std::vector<uint8_t>& pps);
    bool ParseH264ExtraData(const uint8_t* data, size_t size, std::vector<uint8_t>& sps, std::vector<uint8_t>& pps);

    // AAC配置处理
    bool SetAACConfig(const std::vector<uint8_t>& config);
    bool GenerateAACConfig(int sample_rate, int channels, std::vector<uint8_t>& config);
    
    // 获取当前录像信息
    RecordingInfo GetCurrentRecordingInfo() const;
    std::vector<RecordingInfo> GetAllRecordings() const;
    
    // 文件管理
    bool DeleteRecording(const std::string& hash);
    bool FindRecordingByHash(const std::string& hash, RecordingInfo& info) const;
    std::vector<RecordingInfo> SearchRecordingsByTag(const std::string& tag, const std::string& value) const;
    
    // 添加标签
    bool AddTag(const std::string& key, const std::string& value);
    bool RemoveTag(const std::string& key);
    
    // 回调函数类型
    using RecordingStartCallback = std::function<void(const std::string& filename)>;
    using RecordingStopCallback = std::function<void(const RecordingInfo& info)>;
    using ErrorCallback = std::function<void(const std::string& error)>;
    
    void SetRecordingStartCallback(RecordingStartCallback callback) { m_start_callback = callback; }
    void SetRecordingStopCallback(RecordingStopCallback callback) { m_stop_callback = callback; }
    void SetErrorCallback(ErrorCallback callback) { m_error_callback = callback; }

private:
    RecordingConfig m_config;
    std::atomic<RecordingState> m_state{RecordingState::STOPPED};
    
    // MP4写入器
    struct mp4_writer_t* m_mp4_writer;
    FILE* m_output_file;
    
    // 轨道信息
    int m_video_track;
    int m_audio_track;
    bool m_video_track_added;
    bool m_audio_track_added;
    
    // 当前录像信息
    RecordingInfo m_current_recording;
    std::string m_current_session_id;
    
    // 线程和同步
    std::thread m_worker_thread;
    std::mutex m_frame_mutex;
    std::queue<MediaFrame> m_frame_queue;
    std::atomic<bool> m_running{false};
    
    // 统计信息
    uint64_t m_bytes_written;
    std::chrono::steady_clock::time_point m_recording_start_time;
    std::map<std::string, std::string> m_current_tags;

    // 编码参数
    std::vector<uint8_t> m_video_sps;
    std::vector<uint8_t> m_video_pps;
    std::vector<uint8_t> m_audio_config;
    std::vector<uint8_t> m_h264_extra_data;
    bool m_video_params_set;
    bool m_audio_params_set;
    
    // 私有方法
    void WorkerThread();
    bool CreateNewFile();
    bool CloseCurrentFile();
    bool ProcessFrame(const MediaFrame& frame);
    bool CheckFileLimits();
    void CalculateFileHash();
    std::string GenerateFilename();
    bool SaveRecordingInfo(const RecordingInfo& info);
    bool LoadRecordingDatabase();

    // H.264帧处理
    bool ExtractH264ParamsFromFrame(const std::vector<uint8_t>& frame_data,
                                   std::vector<uint8_t>& sps, std::vector<uint8_t>& pps);
    bool ConvertAnnexBToAVCC(const std::vector<uint8_t>& annexb_data, std::vector<uint8_t>& avcc_data);

    // AAC帧处理
    bool ExtractAACConfig(const std::vector<uint8_t>& frame_data, std::vector<uint8_t>& config);
    
    // 回调函数
    RecordingStartCallback m_start_callback;
    RecordingStopCallback m_stop_callback;
    ErrorCallback m_error_callback;
    
    // 录像数据库
    std::vector<RecordingInfo> m_recordings_db;
    mutable std::mutex m_db_mutex;
    std::string m_db_file;
    
    // MOV buffer callbacks
    static int mov_buffer_read(void* param, void* data, uint64_t bytes);
    static int mov_buffer_write(void* param, const void* data, uint64_t bytes);
    static int mov_buffer_seek(void* param, int64_t offset);
    static int64_t mov_buffer_tell(void* param);
};

#endif // MP4_RECORDER_H
