﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
    <Filter Include="payload">
      <UniqueIdentifier>{587ea5e2-9fdd-4385-a308-4574bc666911}</UniqueIdentifier>
    </Filter>
    <Filter Include="rtpext">
      <UniqueIdentifier>{b23300c1-ca20-4952-b465-583076d0e7f0}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="source\rtcp-app.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-bye.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-interval.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-rr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-sdec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-sr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-member-list.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-member.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-ssrc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-time.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h264-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h264-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-profile.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-packet.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h265-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h265-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-payload.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-ts-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-ts-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mpeg1or2es-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mpeg1or2es-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-payload-helper.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mp4a-latm-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mp4v-es-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mp4v-es-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mp4a-latm-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mpeg4-generic-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-mpeg4-generic-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-vp9-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-vp9-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-vp8-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-vp8-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-queue.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-av1-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-av1-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-ps-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="source\rtp-demuxer.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h264-bitstream.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-absolute-capture-time.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-abs-send-time.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-color-space.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-frame-marking.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-inband-cn.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-playout-delay.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-sdes.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-ssrc-audio-level.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-toffset.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-transport-wide-cc.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-video-content-type.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-video-frame-tracking-id.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-video-layers-allocation.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-video-orientation.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-video-timing.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-xr.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-psfb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtcp-rtpfb.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="rtpext\rtp-ext-csrc-audio-level.c">
      <Filter>rtpext</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h266-pack.c">
      <Filter>payload</Filter>
    </ClCompile>
    <ClCompile Include="payload\rtp-h266-unpack.c">
      <Filter>payload</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\rtcp-header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-member-list.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-member.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-packet.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-param.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-profile.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-queue.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="payload\rtp-payload-internal.h">
      <Filter>payload</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-payload.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="payload\rtp-payload-helper.h">
      <Filter>payload</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-demuxer.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-ext.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtp-header-extension.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>