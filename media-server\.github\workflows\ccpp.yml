name: C/C++ CI

on: [push]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v2
        with:
          path: media-server

      - name: Sdk
        uses: actions/checkout@v2
        with:
          repository: ireader/sdk
          path: sdk
          
      - name: avcodec
        uses: actions/checkout@v2
        with:
          repository: ireader/avcodec
          path: avcodec

      - name: Make
        run: make
        working-directory: media-server

#      - name: Make check
#        run: make test
#        working-directory: media-server
