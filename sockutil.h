#ifndef SOCKUTIL_H
#define SOC<PERSON><PERSON>IL_H

#include <stdint.h>
#include <stddef.h>

#ifdef _WIN32
    #include <winsock2.h>
    #include <ws2tcpip.h>
    typedef SOCKET socket_t;
    #define socket_invalid INVALID_SOCKET
#else
    #include <sys/socket.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <unistd.h>
    #include <fcntl.h>
    #include <errno.h>
    typedef int socket_t;
    #define socket_invalid -1
    #define SOMAXCONN 128
#endif

#ifdef __cplusplus
extern "C" {
#endif

// Socket初始化和清理
int socket_init(void);
int socket_cleanup(void);

// 创建和关闭
socket_t socket_tcp(void);
socket_t socket_udp(void);
int socket_close(socket_t sock);

// 监听和连接
socket_t socket_tcp_listen_ipv4(const char* ip, uint16_t port, int backlog);
socket_t socket_tcp_listen_ipv6(const char* ip, uint16_t port, int backlog);
socket_t socket_accept(socket_t sock, struct sockaddr_storage* addr, socklen_t* addrlen);
socket_t socket_connect_host(const char* host, uint16_t port, int timeout);

// 发送和接收
int socket_send(socket_t sock, const void* buf, size_t len, int flags);
int socket_recv(socket_t sock, void* buf, size_t len, int flags);
int socket_send_all_by_time(socket_t sock, const void* buf, size_t len, int flags, int timeout);

// 向量发送
typedef struct {
    void* iov_base;
    size_t iov_len;
} socket_bufvec_t;

void socket_setbufvec(socket_bufvec_t* vec, int idx, void* base, size_t len);
int socket_send_v_all_by_time(socket_t sock, const socket_bufvec_t* vec, int n, int flags, int timeout);

// 设置选项
int socket_setnonblock(socket_t sock, int nonblock);
int socket_setkeepalive(socket_t sock, int enable);
int socket_setreuse(socket_t sock, int enable);

// 地址转换
int socket_addr_from(struct sockaddr_storage* ss, socklen_t* len, const char* ip, uint16_t port);
int socket_addr_to(const struct sockaddr_storage* ss, socklen_t len, char* ip, uint16_t* port);

// 获取本地地址
int socket_getname(socket_t sock, char* ip, uint16_t* port);
int socket_getpeername(socket_t sock, char* ip, uint16_t* port);

#ifdef __cplusplus
}
#endif

#endif // SOCKUTIL_H