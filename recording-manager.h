#ifndef RECORDING_MANAGER_H
#define RECORDING_MANAGER_H

#include <string>
#include <vector>
#include <map>
#include <memory>
#include <thread>
#include <mutex>
#include <atomic>
#include <functional>
#include <chrono>
#include "mp4-recorder.h"
#include "security-chip-sdk.h"

// 录像任务配置
struct RecordingTask {
    std::string task_id;                    // 任务ID
    std::string device_id;                  // 设备ID
    SecurityChipConfig chip_config;         // 芯片配置
    RecordingConfig recording_config;       // 录像配置
    SecurityChipSDKFactory::SDKType sdk_type; // SDK类型
    
    // 调度配置
    bool continuous_recording = true;       // 连续录像
    std::vector<std::pair<int, int>> time_ranges; // 时间段 (小时, 分钟)
    std::vector<int> weekdays;             // 星期几 (0=周日, 1=周一, ...)
    
    // 触发录像配置
    bool motion_detection = false;          // 移动侦测触发
    bool alarm_trigger = false;             // 报警触发
    int pre_record_seconds = 5;             // 预录时间
    int post_record_seconds = 10;           // 后录时间
    
    std::map<std::string, std::string> tags; // 任务标签
};

// 录像任务状态
enum class TaskState {
    STOPPED,
    STARTING,
    RUNNING,
    STOPPING,
    ERROR,
    SCHEDULED  // 计划中（等待时间段）
};

// 录像任务信息
struct TaskInfo {
    RecordingTask task;
    TaskState state;
    std::string error_message;
    std::chrono::system_clock::time_point last_start_time;
    std::chrono::system_clock::time_point last_stop_time;
    uint64_t total_recorded_bytes;
    uint32_t total_recorded_duration;
    std::vector<RecordingInfo> recordings;
};

// 录像管理器
class RecordingManager {
public:
    RecordingManager();
    ~RecordingManager();
    
    // 任务管理
    bool AddTask(const RecordingTask& task);
    bool RemoveTask(const std::string& task_id);
    bool UpdateTask(const RecordingTask& task);
    bool StartTask(const std::string& task_id);
    bool StopTask(const std::string& task_id);
    bool StartAllTasks();
    bool StopAllTasks();
    
    // 获取任务信息
    std::vector<TaskInfo> GetAllTasks() const;
    bool GetTaskInfo(const std::string& task_id, TaskInfo& info) const;
    std::vector<std::string> GetRunningTasks() const;
    
    // 录像文件管理
    std::vector<RecordingInfo> GetAllRecordings() const;
    std::vector<RecordingInfo> GetRecordingsByDevice(const std::string& device_id) const;
    std::vector<RecordingInfo> GetRecordingsByTimeRange(
        const std::chrono::system_clock::time_point& start,
        const std::chrono::system_clock::time_point& end) const;
    std::vector<RecordingInfo> SearchRecordings(const std::string& query) const;
    
    // 文件操作
    bool DeleteRecording(const std::string& hash);
    bool DeleteRecordingsByDevice(const std::string& device_id);
    bool DeleteRecordingsByTimeRange(
        const std::chrono::system_clock::time_point& start,
        const std::chrono::system_clock::time_point& end);
    
    // 存储管理
    uint64_t GetTotalStorageUsed() const;
    uint64_t GetAvailableStorage() const;
    bool CleanupOldRecordings(uint32_t days_to_keep);
    bool CleanupByStorageLimit(uint64_t max_storage_bytes);
    
    // 统计信息
    struct Statistics {
        uint32_t total_tasks;
        uint32_t running_tasks;
        uint32_t total_recordings;
        uint64_t total_storage_used;
        uint32_t recordings_today;
        uint32_t recordings_this_week;
        uint32_t recordings_this_month;
    };
    Statistics GetStatistics() const;
    
    // 事件回调
    using TaskStateChangeCallback = std::function<void(const std::string& task_id, TaskState old_state, TaskState new_state)>;
    using RecordingCompleteCallback = std::function<void(const std::string& task_id, const RecordingInfo& info)>;
    using ErrorCallback = std::function<void(const std::string& task_id, const std::string& error)>;
    using StorageWarningCallback = std::function<void(uint64_t used_bytes, uint64_t total_bytes)>;
    
    void SetTaskStateChangeCallback(TaskStateChangeCallback callback) { m_task_state_callback = callback; }
    void SetRecordingCompleteCallback(RecordingCompleteCallback callback) { m_recording_complete_callback = callback; }
    void SetErrorCallback(ErrorCallback callback) { m_error_callback = callback; }
    void SetStorageWarningCallback(StorageWarningCallback callback) { m_storage_warning_callback = callback; }
    
    // 配置管理
    bool SaveConfiguration(const std::string& config_file);
    bool LoadConfiguration(const std::string& config_file);
    
    // 启动/停止管理器
    bool Start();
    bool Stop();
    bool IsRunning() const { return m_running; }

private:
    // 任务管理
    std::map<std::string, TaskInfo> m_tasks;
    std::map<std::string, std::unique_ptr<SecurityChipSDK>> m_sdks;
    std::map<std::string, std::unique_ptr<MP4Recorder>> m_recorders;
    mutable std::mutex m_tasks_mutex;
    
    // 管理线程
    std::thread m_manager_thread;
    std::atomic<bool> m_running{false};
    
    // 录像数据库
    std::vector<RecordingInfo> m_all_recordings;
    mutable std::mutex m_recordings_mutex;
    std::string m_recordings_db_file;
    
    // 配置
    std::string m_config_file;
    uint64_t m_storage_warning_threshold;
    uint32_t m_cleanup_interval_hours;
    
    // 回调函数
    TaskStateChangeCallback m_task_state_callback;
    RecordingCompleteCallback m_recording_complete_callback;
    ErrorCallback m_error_callback;
    StorageWarningCallback m_storage_warning_callback;
    
    // 私有方法
    void ManagerThread();
    void ProcessTask(const std::string& task_id);
    bool ShouldStartRecording(const RecordingTask& task) const;
    bool IsInTimeRange(const RecordingTask& task) const;
    bool IsInWeekday(const RecordingTask& task) const;
    
    void SetTaskState(const std::string& task_id, TaskState new_state);
    void OnRecordingComplete(const std::string& task_id, const RecordingInfo& info);
    void OnTaskError(const std::string& task_id, const std::string& error);
    
    void CheckStorageUsage();
    void LoadAllRecordings();
    void SaveAllRecordings();
    
    // SDK和录像器管理
    bool CreateSDKForTask(const std::string& task_id);
    bool CreateRecorderForTask(const std::string& task_id);
    void DestroySDKForTask(const std::string& task_id);
    void DestroyRecorderForTask(const std::string& task_id);
    
    // 流数据回调
    void OnStreamData(const std::string& task_id, const MediaFrame& frame);
    void OnConnectionStatus(const std::string& task_id, bool connected, const std::string& error);
};

#endif // RECORDING_MANAGER_H
