#include "RtspServer.h"
#include "rtsp-header-session.h"
#include "sdp.h"
#include "rtp-payload.h"
#include "rtp.h"
#include <iostream>
#include <sstream>
#include <cstring>
#include <random>

RtspServer::RtspServer(std::shared_ptr<MediaSourceManager> sourceManager)
    : m_sourceManager(sourceManager) {
    socket_init();
}

RtspServer::~RtspServer() {
    Stop();
    socket_cleanup();
}

bool RtspServer::Start(int port) {
    if (m_running) {
        return false;
    }
    
    m_socket = socket_tcp_listen_ipv4(NULL, port, SOMAXCONN);
    if (socket_invalid == m_socket) {
        std::cerr << "Failed to create RTSP listening socket on port " << port << std::endl;
        return false;
    }
    
    m_running = true;
    m_acceptThread = std::thread(&RtspServer::AcceptThread, this);
    
    return true;
}

void RtspServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    if (socket_invalid != m_socket) {
        socket_close(m_socket);
        m_socket = socket_invalid;
    }
    
    if (m_acceptThread.joinable()) {
        m_acceptThread.join();
    }
    
    // 停止所有会话
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (auto& pair : m_sessions) {
            pair.second->Stop();
        }
        m_sessions.clear();
    }
}

void RtspServer::AcceptThread() {
    while (m_running) {
        struct sockaddr_storage addr;
        socklen_t addrlen = sizeof(addr);
        
        socket_t client = socket_accept(m_socket, &addr, &addrlen);
        if (socket_invalid == client) {
            continue;
        }
        
        // 创建新线程处理客户端
        std::thread clientThread(&RtspServer::HandleClient, this, client, addr);
        clientThread.detach();
    }
}

void RtspServer::HandleClient(socket_t client, const struct sockaddr_storage& addr) {
    // 创建RTSP服务器处理器
    struct rtsp_handler_t handler;
    memset(&handler, 0, sizeof(handler));
    handler.ondescribe = OnDescribe;
    handler.onsetup = OnSetup;
    handler.onplay = OnPlay;
    handler.onpause = OnPause;
    handler.onteardown = OnTeardown;
    handler.onannounce = OnAnnounce;
    handler.onrecord = OnRecord;
    handler.onoptions = OnOptions;
    handler.ongetparameter = OnGetParameter;
    handler.onsetparameter = OnSetParameter;
    
    char ip[65];
    uint16_t port;
    socket_addr_to(const_cast<struct sockaddr_storage*>(&addr), sizeof(addr), ip, &port);
    
    rtsp_server_t* rtsp = rtsp_server_create(ip, port, &handler, this, this);
    if (!rtsp) {
        socket_close(client);
        return;
    }
    
    // 创建会话
    auto session = std::make_shared<RtspSession>(client, addr, rtsp, m_sourceManager);
    
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_sessions[session.get()] = session;
    }
    
    // 启动会话
    session->Start();
    
    // 会话结束后清理
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_sessions.erase(session.get());
    }
    
    rtsp_server_destroy(rtsp);
}

// RTSP回调实现
int RtspServer::OnDescribe(void* ptr, rtsp_server_t* rtsp, const char* uri) {
    // TODO: 实现DESCRIBE请求处理
    return rtsp_server_reply_describe(rtsp, 404, nullptr);
}

int RtspServer::OnSetup(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                        const struct rtsp_header_transport_t transports[], size_t num) {
    // TODO: 实现SETUP请求处理
    return rtsp_server_reply_setup(rtsp, 404, nullptr, nullptr);
}

int RtspServer::OnPlay(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                       const int64_t *npt, const double *scale) {
    // TODO: 实现PLAY请求处理
    return rtsp_server_reply_play(rtsp, 404, nullptr, nullptr, nullptr);
}

int RtspServer::OnPause(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                        const int64_t *npt) {
    // TODO: 实现PAUSE请求处理
    return rtsp_server_reply_pause(rtsp, 404);
}

int RtspServer::OnTeardown(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session) {
    // TODO: 实现TEARDOWN请求处理
    return rtsp_server_reply_teardown(rtsp, 200);
}

int RtspServer::OnAnnounce(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* sdp, int len) {
    // TODO: 实现ANNOUNCE请求处理
    return rtsp_server_reply_announce(rtsp, 501);
}

int RtspServer::OnRecord(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                         const int64_t *npt, const double *scale) {
    // TODO: 实现RECORD请求处理
    return rtsp_server_reply_record(rtsp, 501, nullptr, nullptr, nullptr);
}

int RtspServer::OnOptions(void* ptr, rtsp_server_t* rtsp, const char* uri) {
    return rtsp_server_reply_options(rtsp, 200);
}

int RtspServer::OnGetParameter(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                               const void* content, int bytes) {
    return rtsp_server_reply_get_parameter(rtsp, 200, nullptr, 0);
}

int RtspServer::OnSetParameter(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session,
                               const void* content, int bytes) {
    return rtsp_server_reply_set_parameter(rtsp, 200);
}

// RtspSession实现
RtspSession::RtspSession(socket_t socket, const struct sockaddr_storage& addr,
                         rtsp_server_t* rtsp, std::shared_ptr<MediaSourceManager> sourceManager)
    : m_socket(socket), m_addr(addr), m_rtsp(rtsp), m_sourceManager(sourceManager) {
    
    // 生成会话ID
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(100000, 999999);
    m_sessionId = std::to_string(dis(gen));
}

RtspSession::~RtspSession() {
    Stop();
}

void RtspSession::Start() {
    m_running = true;
    m_thread = std::thread(&RtspSession::NetworkThread, this);
}

void RtspSession::Stop() {
    m_running = false;
    
    if (m_socket != socket_invalid) {
        socket_close(m_socket);
        m_socket = socket_invalid;
    }
    
    if (m_thread.joinable()) {
        m_thread.join();
    }
    
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
    }
}

void RtspSession::NetworkThread() {
    uint8_t buffer[4096];
    
    while (m_running) {
        int r = socket_recv(m_socket, buffer, sizeof(buffer), 0);
        if (r <= 0) {
            break;
        }
        
        size_t bytes = r;
        if (0 != rtsp_server_input(m_rtsp, buffer, &bytes)) {
            break;
        }
    }
    
    m_running = false;
}

int RtspSession::Send(const void* data, size_t bytes) {
    if (!m_running || m_socket == socket_invalid) {
        return -1;
    }
    
    return socket_send_all_by_time(m_socket, data, bytes, 0, 2000);
}

int RtspSession::Describe(const char* uri) {
    // 查找媒体源
    m_streamPath = uri;
    auto source = m_sourceManager->FindSource(uri);
    if (!source) {
        return rtsp_server_reply_describe(m_rtsp, 404, nullptr);
    }
    
    m_mediaSource = source;
    m_mediaInfo = source->GetMediaInfo();
    
    // 生成SDP
    GenerateSDP();
    
    return rtsp_server_reply_describe(m_rtsp, 200, m_sdp.c_str());
}

int RtspSession::Setup(const char* uri, const struct rtsp_header_transport_t transports[], size_t num) {
    // TODO: 实现SETUP逻辑
    return rtsp_server_reply_setup(m_rtsp, 200, m_sessionId.c_str(), nullptr);
}

int RtspSession::Play(const char* uri) {
    if (!m_mediaSource) {
        return rtsp_server_reply_play(m_rtsp, 404, nullptr, nullptr, nullptr);
    }
    
    // 添加到媒体源
    m_mediaSource->AddSink(weak_from_this());
    
    return rtsp_server_reply_play(m_rtsp, 200, nullptr, nullptr, nullptr);
}

int RtspSession::Pause(const char* uri) {
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
    }
    
    return rtsp_server_reply_pause(m_rtsp, 200);
}

int RtspSession::Teardown(const char* uri) {
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
        m_mediaSource.reset();
    }
    
    return rtsp_server_reply_teardown(m_rtsp, 200);
}

void RtspSession::OnMediaPacket(const MediaPacket& packet) {
    if (!m_running) {
        return;
    }
    
    // 找到对应的track
    int trackId = -1;
    switch (packet.type) {
    case MediaPacket::VIDEO_H264:
    case MediaPacket::VIDEO_H265:
        trackId = 0; // 视频track
        break;
        
    case MediaPacket::AUDIO_AAC:
    case MediaPacket::AUDIO_G711A:
    case MediaPacket::AUDIO_G711U:
        trackId = 1; // 音频track
        break;
        
    default:
        return;
    }
    
    SendRTPPacket(trackId, packet);
}

void RtspSession::OnMediaInfo(const MediaInfo& info) {
    m_mediaInfo = info;
    GenerateSDP();
}

void RtspSession::GenerateSDP() {
    std::stringstream ss;
    
    // SDP会话描述
    ss << "v=0\r\n";
    ss << "o=- 0 0 IN IP4 0.0.0.0\r\n";
    ss << "s=RTSP Media Server\r\n";
    ss << "c=IN IP4 0.0.0.0\r\n";
    ss << "t=0 0\r\n";
    
    int trackId = 0;
    
    // 视频媒体描述
    if (m_mediaInfo.has_video) {
        ss << "m=video 0 RTP/AVP ";
        
        if (m_mediaInfo.video_codec == 1) { // H264
            ss << "96\r\n";
            ss << "a=rtpmap:96 H264/90000\r\n";
            ss << "a=fmtp:96 profile-level-id=42e01e;packetization-mode=1\r\n";
        } else if (m_mediaInfo.video_codec == 2) { // H265
            ss << "97\r\n";
            ss << "a=rtpmap:97 H265/90000\r\n";
        }
        
        ss << "a=control:track" << trackId++ << "\r\n";
    }
    
    // 音频媒体描述
    if (m_mediaInfo.has_audio) {
        ss << "m=audio 0 RTP/AVP ";
        
        if (m_mediaInfo.audio_codec == 1) { // AAC
            ss << "98\r\n";
            ss << "a=rtpmap:98 MPEG4-GENERIC/" << m_mediaInfo.audio_sample_rate << "/" << m_mediaInfo.audio_channels << "\r\n";
            ss << "a=fmtp:98 streamtype=5;profile-level-id=1;mode=AAC-hbr\r\n";
        } else if (m_mediaInfo.audio_codec == 2) { // G711A
            ss << "8\r\n";
            ss << "a=rtpmap:8 PCMA/8000\r\n";
        } else if (m_mediaInfo.audio_codec == 3) { // G711U
            ss << "0\r\n";
            ss << "a=rtpmap:0 PCMU/8000\r\n";
        }
        
        ss << "a=control:track" << trackId++ << "\r\n";
    }
    
    m_sdp = ss.str();
}

void RtspSession::SendRTPPacket(int trackId, const MediaPacket& packet) {
    if (trackId < 0 || trackId >= m_tracks.size()) {
        return;
    }
    
    auto& track = m_tracks[trackId];
    if (!track.transport) {
        return;
    }
    
    // TODO: 实现RTP打包和发送
}