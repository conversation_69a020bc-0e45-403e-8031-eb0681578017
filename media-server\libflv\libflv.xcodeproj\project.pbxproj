// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		0C9FC5FA298D04A500FB85DD /* vvc-annexbtomp4.c in Sources */ = {isa = PBXBuildFile; fileRef = 0C9FC5F8298D04A500FB85DD /* vvc-annexbtomp4.c */; };
		0C9FC5FB298D04A500FB85DD /* vvc-mp4toannexb.c in Sources */ = {isa = PBXBuildFile; fileRef = 0C9FC5F9298D04A500FB85DD /* vvc-mp4toannexb.c */; };
		0C9FC601298D056E00FB85DD /* mpeg4-vvc.c in Sources */ = {isa = PBXBuildFile; fileRef = 0C9FC600298D056E00FB85DD /* mpeg4-vvc.c */; };
		4605125A24D6B4D500B04B70 /* opus-head.c in Sources */ = {isa = PBXBuildFile; fileRef = 4605125924D6B4D500B04B70 /* opus-head.c */; };
		46733799219B2315009F658F /* mpeg4-aac-asc.c in Sources */ = {isa = PBXBuildFile; fileRef = 46733798219B2315009F658F /* mpeg4-aac-asc.c */; };
		468B915C23B8ADAA00EA99A3 /* aom-av1.c in Sources */ = {isa = PBXBuildFile; fileRef = 468B915B23B8ADAA00EA99A3 /* aom-av1.c */; };
		468B916023B8AE3100EA99A3 /* flv-header.c in Sources */ = {isa = PBXBuildFile; fileRef = 468B915F23B8AE3100EA99A3 /* flv-header.c */; };
		46C5B2DB2183EDA200419E57 /* mpeg4-mp4toannexb.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2CB2183EDA200419E57 /* mpeg4-mp4toannexb.c */; };
		46C5B2DC2183EDA200419E57 /* mp3-header.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2CC2183EDA200419E57 /* mp3-header.c */; };
		46C5B2DD2183EDA200419E57 /* flv-writer.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2CD2183EDA200419E57 /* flv-writer.c */; };
		46C5B2DE2183EDA200419E57 /* amf0.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2CE2183EDA200419E57 /* amf0.c */; };
		46C5B2DF2183EDA200419E57 /* hevc-annexbtomp4.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2CF2183EDA200419E57 /* hevc-annexbtomp4.c */; };
		46C5B2E02183EDA200419E57 /* mpeg4-hevc.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D02183EDA200419E57 /* mpeg4-hevc.c */; };
		46C5B2E12183EDA200419E57 /* flv-demuxer.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D12183EDA200419E57 /* flv-demuxer.c */; };
		46C5B2E22183EDA200419E57 /* amf3.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D22183EDA200419E57 /* amf3.c */; };
		46C5B2E32183EDA200419E57 /* mpeg4-aac.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D32183EDA200419E57 /* mpeg4-aac.c */; };
		46C5B2E42183EDA200419E57 /* flv-reader.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D42183EDA200419E57 /* flv-reader.c */; };
		46C5B2E52183EDA200419E57 /* flv-demuxer-script.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D52183EDA200419E57 /* flv-demuxer-script.c */; };
		46C5B2E62183EDA200419E57 /* mpeg4-annexbtomp4.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D62183EDA200419E57 /* mpeg4-annexbtomp4.c */; };
		46C5B2E72183EDA200419E57 /* flv-parser.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D72183EDA200419E57 /* flv-parser.c */; };
		46C5B2E82183EDA200419E57 /* flv-muxer.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D82183EDA200419E57 /* flv-muxer.c */; };
		46C5B2E92183EDA200419E57 /* mpeg4-avc.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2D92183EDA200419E57 /* mpeg4-avc.c */; };
		46C5B2EA2183EDA200419E57 /* hevc-mp4toannexb.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B2DA2183EDA200419E57 /* hevc-mp4toannexb.c */; };
		46E55E5324694DE000D8BDBA /* webm-vpx.c in Sources */ = {isa = PBXBuildFile; fileRef = 46E55E5224694DE000D8BDBA /* webm-vpx.c */; };
		4CEE46E727A70C1900740460 /* riff-acm.c in Sources */ = {isa = PBXBuildFile; fileRef = 4CEE46E627A70C1900740460 /* riff-acm.c */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		0C9FC5F8298D04A500FB85DD /* vvc-annexbtomp4.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "vvc-annexbtomp4.c"; sourceTree = "<group>"; };
		0C9FC5F9298D04A500FB85DD /* vvc-mp4toannexb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "vvc-mp4toannexb.c"; sourceTree = "<group>"; };
		0C9FC600298D056E00FB85DD /* mpeg4-vvc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-vvc.c"; sourceTree = "<group>"; };
		4605125924D6B4D500B04B70 /* opus-head.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "opus-head.c"; sourceTree = "<group>"; };
		46733798219B2315009F658F /* mpeg4-aac-asc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-aac-asc.c"; sourceTree = "<group>"; };
		468B915B23B8ADAA00EA99A3 /* aom-av1.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "aom-av1.c"; sourceTree = "<group>"; };
		468B915F23B8AE3100EA99A3 /* flv-header.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-header.c"; sourceTree = "<group>"; };
		46C5B21C2183EA7400419E57 /* libflv.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libflv.a; sourceTree = BUILT_PRODUCTS_DIR; };
		46C5B2CB2183EDA200419E57 /* mpeg4-mp4toannexb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-mp4toannexb.c"; sourceTree = "<group>"; };
		46C5B2CC2183EDA200419E57 /* mp3-header.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mp3-header.c"; sourceTree = "<group>"; };
		46C5B2CD2183EDA200419E57 /* flv-writer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-writer.c"; sourceTree = "<group>"; };
		46C5B2CE2183EDA200419E57 /* amf0.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = amf0.c; sourceTree = "<group>"; };
		46C5B2CF2183EDA200419E57 /* hevc-annexbtomp4.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hevc-annexbtomp4.c"; sourceTree = "<group>"; };
		46C5B2D02183EDA200419E57 /* mpeg4-hevc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-hevc.c"; sourceTree = "<group>"; };
		46C5B2D12183EDA200419E57 /* flv-demuxer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-demuxer.c"; sourceTree = "<group>"; };
		46C5B2D22183EDA200419E57 /* amf3.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = amf3.c; sourceTree = "<group>"; };
		46C5B2D32183EDA200419E57 /* mpeg4-aac.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-aac.c"; sourceTree = "<group>"; };
		46C5B2D42183EDA200419E57 /* flv-reader.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-reader.c"; sourceTree = "<group>"; };
		46C5B2D52183EDA200419E57 /* flv-demuxer-script.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-demuxer-script.c"; sourceTree = "<group>"; };
		46C5B2D62183EDA200419E57 /* mpeg4-annexbtomp4.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-annexbtomp4.c"; sourceTree = "<group>"; };
		46C5B2D72183EDA200419E57 /* flv-parser.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-parser.c"; sourceTree = "<group>"; };
		46C5B2D82183EDA200419E57 /* flv-muxer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "flv-muxer.c"; sourceTree = "<group>"; };
		46C5B2D92183EDA200419E57 /* mpeg4-avc.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "mpeg4-avc.c"; sourceTree = "<group>"; };
		46C5B2DA2183EDA200419E57 /* hevc-mp4toannexb.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "hevc-mp4toannexb.c"; sourceTree = "<group>"; };
		46C5B2EB2183EDB400419E57 /* include */ = {isa = PBXFileReference; lastKnownFileType = folder; path = include; sourceTree = "<group>"; };
		46E55E5224694DE000D8BDBA /* webm-vpx.c */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.c; path = "webm-vpx.c"; sourceTree = "<group>"; };
		4CEE46E627A70C1900740460 /* riff-acm.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "riff-acm.c"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		46C5B21A2183EA7400419E57 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		46C5B2132183EA7300419E57 = {
			isa = PBXGroup;
			children = (
				46C5B2EB2183EDB400419E57 /* include */,
				46C5B2CA2183EDA200419E57 /* source */,
				46C5B21D2183EA7400419E57 /* Products */,
			);
			sourceTree = "<group>";
		};
		46C5B21D2183EA7400419E57 /* Products */ = {
			isa = PBXGroup;
			children = (
				46C5B21C2183EA7400419E57 /* libflv.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		46C5B2CA2183EDA200419E57 /* source */ = {
			isa = PBXGroup;
			children = (
				0C9FC600298D056E00FB85DD /* mpeg4-vvc.c */,
				0C9FC5F8298D04A500FB85DD /* vvc-annexbtomp4.c */,
				0C9FC5F9298D04A500FB85DD /* vvc-mp4toannexb.c */,
				4CEE46E627A70C1900740460 /* riff-acm.c */,
				46C5B2CE2183EDA200419E57 /* amf0.c */,
				46C5B2D22183EDA200419E57 /* amf3.c */,
				468B915B23B8ADAA00EA99A3 /* aom-av1.c */,
				46C5B2D52183EDA200419E57 /* flv-demuxer-script.c */,
				46C5B2D12183EDA200419E57 /* flv-demuxer.c */,
				468B915F23B8AE3100EA99A3 /* flv-header.c */,
				46C5B2D82183EDA200419E57 /* flv-muxer.c */,
				46C5B2D72183EDA200419E57 /* flv-parser.c */,
				46C5B2D42183EDA200419E57 /* flv-reader.c */,
				46C5B2CD2183EDA200419E57 /* flv-writer.c */,
				46C5B2CF2183EDA200419E57 /* hevc-annexbtomp4.c */,
				46C5B2DA2183EDA200419E57 /* hevc-mp4toannexb.c */,
				46C5B2CC2183EDA200419E57 /* mp3-header.c */,
				46733798219B2315009F658F /* mpeg4-aac-asc.c */,
				46C5B2D32183EDA200419E57 /* mpeg4-aac.c */,
				46C5B2D62183EDA200419E57 /* mpeg4-annexbtomp4.c */,
				46C5B2D92183EDA200419E57 /* mpeg4-avc.c */,
				46C5B2D02183EDA200419E57 /* mpeg4-hevc.c */,
				46C5B2CB2183EDA200419E57 /* mpeg4-mp4toannexb.c */,
				4605125924D6B4D500B04B70 /* opus-head.c */,
				46E55E5224694DE000D8BDBA /* webm-vpx.c */,
			);
			path = source;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		46C5B2182183EA7400419E57 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		46C5B21B2183EA7400419E57 /* libflv */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46C5B2202183EA7400419E57 /* Build configuration list for PBXNativeTarget "libflv" */;
			buildPhases = (
				46C5B2182183EA7400419E57 /* Headers */,
				46C5B2192183EA7400419E57 /* Sources */,
				46C5B21A2183EA7400419E57 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = libflv;
			productName = libflv;
			productReference = 46C5B21C2183EA7400419E57 /* libflv.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		46C5B2142183EA7300419E57 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				ORGANIZATIONNAME = ireader;
				TargetAttributes = {
					46C5B21B2183EA7400419E57 = {
						CreatedOnToolsVersion = 10.0;
					};
				};
			};
			buildConfigurationList = 46C5B2172183EA7300419E57 /* Build configuration list for PBXProject "libflv" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 46C5B2132183EA7300419E57;
			productRefGroup = 46C5B21D2183EA7400419E57 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				46C5B21B2183EA7400419E57 /* libflv */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		46C5B2192183EA7400419E57 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				46C5B2E12183EDA200419E57 /* flv-demuxer.c in Sources */,
				46C5B2DB2183EDA200419E57 /* mpeg4-mp4toannexb.c in Sources */,
				46C5B2E92183EDA200419E57 /* mpeg4-avc.c in Sources */,
				468B916023B8AE3100EA99A3 /* flv-header.c in Sources */,
				46C5B2E62183EDA200419E57 /* mpeg4-annexbtomp4.c in Sources */,
				468B915C23B8ADAA00EA99A3 /* aom-av1.c in Sources */,
				0C9FC601298D056E00FB85DD /* mpeg4-vvc.c in Sources */,
				46C5B2E02183EDA200419E57 /* mpeg4-hevc.c in Sources */,
				46C5B2DC2183EDA200419E57 /* mp3-header.c in Sources */,
				46C5B2EA2183EDA200419E57 /* hevc-mp4toannexb.c in Sources */,
				46C5B2DD2183EDA200419E57 /* flv-writer.c in Sources */,
				46C5B2E82183EDA200419E57 /* flv-muxer.c in Sources */,
				0C9FC5FA298D04A500FB85DD /* vvc-annexbtomp4.c in Sources */,
				46C5B2E42183EDA200419E57 /* flv-reader.c in Sources */,
				46C5B2E52183EDA200419E57 /* flv-demuxer-script.c in Sources */,
				0C9FC5FB298D04A500FB85DD /* vvc-mp4toannexb.c in Sources */,
				46C5B2E72183EDA200419E57 /* flv-parser.c in Sources */,
				46C5B2DF2183EDA200419E57 /* hevc-annexbtomp4.c in Sources */,
				46C5B2E22183EDA200419E57 /* amf3.c in Sources */,
				46733799219B2315009F658F /* mpeg4-aac-asc.c in Sources */,
				46C5B2DE2183EDA200419E57 /* amf0.c in Sources */,
				46E55E5324694DE000D8BDBA /* webm-vpx.c in Sources */,
				4605125A24D6B4D500B04B70 /* opus-head.c in Sources */,
				4CEE46E727A70C1900740460 /* riff-acm.c in Sources */,
				46C5B2E32183EDA200419E57 /* mpeg4-aac.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		46C5B21E2183EA7400419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../../sdk/include,
				);
			};
			name = Debug;
		};
		46C5B21F2183EA7400419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../../sdk/include,
				);
			};
			name = Release;
		};
		46C5B2212183EA7400419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Debug;
		};
		46C5B2222183EA7400419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		46C5B2172183EA7300419E57 /* Build configuration list for PBXProject "libflv" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B21E2183EA7400419E57 /* Debug */,
				46C5B21F2183EA7400419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46C5B2202183EA7400419E57 /* Build configuration list for PBXNativeTarget "libflv" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B2212183EA7400419E57 /* Debug */,
				46C5B2222183EA7400419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 46C5B2142183EA7300419E57 /* Project object */;
}
