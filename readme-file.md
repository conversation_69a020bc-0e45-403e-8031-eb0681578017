# MediaServer - 多协议流媒体服务器

基于media-server库实现的多协议流媒体服务器，支持RTMP、RTSP、HTTP-HLS和HTTP-FLV等多种流媒体协议。

## 功能特性

- **多协议支持**
  - RTMP (实时消息传输协议)
  - RTSP (实时流传输协议)
  - HTTP-HLS (HTTP实时流)
  - HTTP-FLV (HTTP-FLV流)

- **编解码支持**
  - 视频：H.264、H.265
  - 音频：AAC、G.711A/U

- **媒体源类型**
  - 实时推流（Live）
  - 文件点播（VOD）

## 编译要求

- C++11或更高版本
- CMake 3.10+ 或 GNU Make
- 依赖库：
  - [media-server](https://github.com/ireader/media-server)
  - [sdk](https://github.com/ireader/sdk)

## 编译安装

### 1. 下载依赖

```bash
# 克隆主项目
git clone https://github.com/your-repo/mediaserver.git
cd mediaserver

# 克隆依赖库（与mediaserver同级目录）
cd ..
git clone https://github.com/ireader/media-server.git
git clone https://github.com/ireader/sdk.git
```

### 2. 编译依赖库

```bash
# 编译SDK
cd sdk
make

# 编译media-server
cd ../media-server
make
```

### 3. 编译MediaServer

#### 使用Make：
```bash
cd ../mediaserver
make
make install
```

#### 使用CMake：
```bash
cd ../mediaserver
mkdir build
cd build
cmake ..
make
make install
```

## 使用方法

### 启动服务器

```bash
./mediaserver
```

默认端口：
- RTMP: 1935
- RTSP: 554
- HTTP: 8080 (HLS和FLV)

### 推流

#### RTMP推流
```bash
ffmpeg -re -i input.mp4 -c copy -f flv rtmp://localhost/live/stream
```

#### RTSP推流
```bash
ffmpeg -re -i input.mp4 -c copy -f rtsp rtsp://localhost/live/stream
```

### 播放

#### RTMP播放
```bash
ffplay rtmp://localhost/live/stream
```

#### RTSP播放
```bash
ffplay rtsp://localhost/live/stream
```

#### HLS播放
在浏览器中打开：
```
http://localhost:8080/hls/live/stream.m3u8
```

#### HTTP-FLV播放
```bash
ffplay http://localhost:8080/flv/live/stream.flv
```

## 配置说明

服务器配置可以通过修改`main.cpp`中的配置参数：

```cpp
MediaServerConfig config;
config.rtmp_port = 1935;      // RTMP端口
config.rtsp_port = 554;       // RTSP端口
config.http_port = 8080;      // HTTP端口
config.worker_threads = 4;     // 工作线程数
```

## 架构设计

### 核心组件

1. **MediaServer** - 主服务器类，管理所有子服务器
2. **MediaSourceManager** - 媒体源管理器，管理所有媒体流
3. **RtmpServer** - RTMP协议服务器
4. **RtspServer** - RTSP协议服务器
5. **HlsServer** - HLS协议服务器
6. **HttpFlvServer** - HTTP-FLV协议服务器

### 媒体源

- **LiveMediaSource** - 实时媒体源，支持推流
- **FileMediaSource** - 文件媒体源，支持点播

### 数据流程

1. 推流端通过RTMP/RTSP推送数据到服务器
2. 服务器解析协议，提取媒体数据
3. 媒体数据存储在MediaSource中
4. 播放端通过各种协议请求数据
5. 服务器将数据封装成对应协议格式发送

## 性能优化

- 使用多线程处理并发连接
- GOP缓存减少关键帧等待时间
- 零拷贝设计减少内存复制
- 智能会话管理，自动清理无效连接

## 故障排查

### 常见问题

1. **端口被占用**
   - 检查端口是否被其他程序占用
   - 修改配置使用其他端口

2. **推流失败**
   - 检查网络连接
   - 确认推流地址正确
   - 查看服务器日志

3. **播放卡顿**
   - 检查网络带宽
   - 调整GOP大小
   - 增加缓冲区大小

## 开发计划

- [ ] 支持更多编码格式（VP8/VP9/AV1）
- [ ] 添加转码功能
- [ ] 实现集群部署
- [ ] 添加Web管理界面
- [ ] 支持录制功能
- [ ] 添加鉴权机制

## 许可证

本项目基于MIT许可证开源。

## 贡献指南

欢迎提交Issue和Pull Request。

## 联系方式

- 项目主页：https://github.com/your-repo/mediaserver
- 问题反馈：https://github.com/your-repo/mediaserver/issues