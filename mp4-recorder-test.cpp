#include <iostream>
#include <cassert>
#include <thread>
#include <chrono>
#include <fstream>
#include <filesystem>
#include "mp4-recorder.h"
#include "security-chip-sdk.h"
#include "recording-manager.h"

// 测试用的模拟数据生成器
class MockDataGenerator {
public:
    MockDataGenerator() : m_running(false) {}
    
    void Start(StreamDataCallback callback) {
        m_callback = callback;
        m_running = true;
        m_thread = std::thread(&MockDataGenerator::GenerateData, this);
    }
    
    void Stop() {
        m_running = false;
        if (m_thread.joinable()) {
            m_thread.join();
        }
    }
    
private:
    void GenerateData() {
        uint32_t frame_count = 0;
        auto start_time = std::chrono::steady_clock::now();
        
        while (m_running) {
            auto now = std::chrono::steady_clock::now();
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                now - start_time).count();
            
            // 生成视频帧 (25fps)
            if (frame_count % 40 == 0) { // 每40ms一帧
                MediaFrame video_frame;
                video_frame.type = MediaFrame::VIDEO_H264;
                video_frame.pts = timestamp;
                video_frame.dts = timestamp;
                video_frame.keyframe = (frame_count % 1000 == 0); // 每1秒一个关键帧
                
                // 模拟H.264数据
                video_frame.data = GenerateH264Frame(video_frame.keyframe);
                
                if (m_callback) {
                    m_callback(video_frame);
                }
            }
            
            // 生成音频帧 (48kHz, 1024 samples per frame)
            if (frame_count % 21 == 0) { // 约21ms一帧音频
                MediaFrame audio_frame;
                audio_frame.type = MediaFrame::AUDIO_AAC;
                audio_frame.pts = timestamp;
                audio_frame.dts = timestamp;
                audio_frame.keyframe = false;
                
                // 模拟AAC数据
                audio_frame.data = GenerateAACFrame();
                
                if (m_callback) {
                    m_callback(audio_frame);
                }
            }
            
            frame_count++;
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
    }
    
    std::vector<uint8_t> GenerateH264Frame(bool keyframe) {
        std::vector<uint8_t> frame;
        
        if (keyframe) {
            // 模拟SPS
            std::vector<uint8_t> sps = {0x00, 0x00, 0x00, 0x01, 0x67, 0x42, 0x00, 0x1E};
            frame.insert(frame.end(), sps.begin(), sps.end());
            
            // 模拟PPS
            std::vector<uint8_t> pps = {0x00, 0x00, 0x00, 0x01, 0x68, 0xCE, 0x3C, 0x80};
            frame.insert(frame.end(), pps.begin(), pps.end());
            
            // 模拟IDR帧
            std::vector<uint8_t> idr = {0x00, 0x00, 0x00, 0x01, 0x65};
            frame.insert(frame.end(), idr.begin(), idr.end());
            
            // 添加一些模拟数据
            for (int i = 0; i < 1000; i++) {
                frame.push_back(rand() % 256);
            }
        } else {
            // 模拟P帧
            std::vector<uint8_t> p_frame = {0x00, 0x00, 0x00, 0x01, 0x41};
            frame.insert(frame.end(), p_frame.begin(), p_frame.end());
            
            // 添加一些模拟数据
            for (int i = 0; i < 500; i++) {
                frame.push_back(rand() % 256);
            }
        }
        
        return frame;
    }
    
    std::vector<uint8_t> GenerateAACFrame() {
        std::vector<uint8_t> frame;
        
        // 模拟AAC帧数据
        for (int i = 0; i < 200; i++) {
            frame.push_back(rand() % 256);
        }
        
        return frame;
    }
    
    std::atomic<bool> m_running;
    std::thread m_thread;
    StreamDataCallback m_callback;
};

// 测试MP4录像器基本功能
bool TestMP4Recorder() {
    std::cout << "Testing MP4Recorder..." << std::endl;
    
    // 创建测试配置
    RecordingConfig config;
    config.output_dir = "./test_recordings";
    config.max_file_size = 1024 * 1024; // 1MB
    config.max_duration = 10; // 10秒
    config.enable_hash_tagging = true;
    config.file_prefix = "test";
    
    // 创建录像器
    MP4Recorder recorder(config);
    
    // 设置回调
    bool recording_started = false;
    bool recording_stopped = false;
    
    recorder.SetRecordingStartCallback([&](const std::string& filename) {
        std::cout << "Recording started: " << filename << std::endl;
        recording_started = true;
    });
    
    recorder.SetRecordingStopCallback([&](const RecordingInfo& info) {
        std::cout << "Recording stopped: " << info.filename << std::endl;
        std::cout << "File size: " << info.file_size << " bytes" << std::endl;
        std::cout << "Duration: " << info.duration << " seconds" << std::endl;
        recording_stopped = true;
    });
    
    // 启动录像
    if (!recorder.StartRecording("test_session")) {
        std::cerr << "Failed to start recording" << std::endl;
        return false;
    }
    
    // 创建模拟数据生成器
    MockDataGenerator generator;
    generator.Start([&](const MediaFrame& frame) {
        if (frame.type == MediaFrame::VIDEO_H264) {
            recorder.WriteVideoFrame(frame);
        } else if (frame.type == MediaFrame::AUDIO_AAC) {
            recorder.WriteAudioFrame(frame);
        }
    });
    
    // 运行5秒
    std::this_thread::sleep_for(std::chrono::seconds(5));
    
    // 停止生成器和录像
    generator.Stop();
    recorder.StopRecording();
    
    // 等待回调
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    // 验证结果
    if (!recording_started || !recording_stopped) {
        std::cerr << "Recording callbacks not triggered" << std::endl;
        return false;
    }
    
    // 检查文件是否存在
    auto recordings = recorder.GetAllRecordings();
    if (recordings.empty()) {
        std::cerr << "No recordings found" << std::endl;
        return false;
    }
    
    for (const auto& recording : recordings) {
        if (!std::filesystem::exists(recording.filepath)) {
            std::cerr << "Recording file not found: " << recording.filepath << std::endl;
            return false;
        }
        
        if (recording.hash_md5.empty() || recording.hash_sha256.empty()) {
            std::cerr << "Hash values not calculated" << std::endl;
            return false;
        }
    }
    
    std::cout << "MP4Recorder test passed!" << std::endl;
    return true;
}

// 测试录像管理器
bool TestRecordingManager() {
    std::cout << "Testing RecordingManager..." << std::endl;
    
    RecordingManager manager;
    
    // 创建测试任务
    RecordingTask task;
    task.task_id = "test_task";
    task.device_id = "test_device";
    task.sdk_type = SecurityChipSDKFactory::GENERIC;
    
    task.chip_config.device_id = "test_device";
    task.chip_config.ip_address = "127.0.0.1";
    task.chip_config.port = 8000;
    
    task.recording_config.output_dir = "./test_recordings";
    task.recording_config.max_file_size = 1024 * 1024;
    task.recording_config.max_duration = 5;
    task.recording_config.file_prefix = "manager_test";
    
    task.continuous_recording = true;
    task.tags["test"] = "true";
    
    // 添加任务
    if (!manager.AddTask(task)) {
        std::cerr << "Failed to add task" << std::endl;
        return false;
    }
    
    // 启动管理器
    if (!manager.Start()) {
        std::cerr << "Failed to start manager" << std::endl;
        return false;
    }
    
    // 获取任务信息
    auto tasks = manager.GetAllTasks();
    if (tasks.size() != 1) {
        std::cerr << "Expected 1 task, got " << tasks.size() << std::endl;
        return false;
    }
    
    // 获取统计信息
    auto stats = manager.GetStatistics();
    if (stats.total_tasks != 1) {
        std::cerr << "Expected 1 total task, got " << stats.total_tasks << std::endl;
        return false;
    }
    
    // 停止管理器
    manager.Stop();
    
    std::cout << "RecordingManager test passed!" << std::endl;
    return true;
}

// 测试配置保存和加载
bool TestConfiguration() {
    std::cout << "Testing Configuration..." << std::endl;
    
    RecordingManager manager;
    
    // 创建测试任务
    RecordingTask task;
    task.task_id = "config_test_task";
    task.device_id = "config_test_device";
    task.tags["location"] = "test_location";
    
    manager.AddTask(task);
    
    // 保存配置
    std::string config_file = "./test_config.json";
    if (!manager.SaveConfiguration(config_file)) {
        std::cerr << "Failed to save configuration" << std::endl;
        return false;
    }
    
    // 创建新的管理器并加载配置
    RecordingManager manager2;
    if (!manager2.LoadConfiguration(config_file)) {
        std::cerr << "Failed to load configuration" << std::endl;
        return false;
    }
    
    // 验证任务是否正确加载
    auto tasks = manager2.GetAllTasks();
    if (tasks.size() != 1) {
        std::cerr << "Expected 1 task after loading, got " << tasks.size() << std::endl;
        return false;
    }
    
    if (tasks[0].task.task_id != "config_test_task") {
        std::cerr << "Task ID mismatch after loading" << std::endl;
        return false;
    }
    
    // 清理测试文件
    std::filesystem::remove(config_file);
    
    std::cout << "Configuration test passed!" << std::endl;
    return true;
}

int main() {
    std::cout << "Starting MP4 Recorder Tests..." << std::endl;
    
    // 创建测试目录
    std::filesystem::create_directories("./test_recordings");
    
    bool all_passed = true;
    
    // 运行测试
    if (!TestMP4Recorder()) {
        all_passed = false;
    }
    
    if (!TestRecordingManager()) {
        all_passed = false;
    }
    
    if (!TestConfiguration()) {
        all_passed = false;
    }
    
    // 清理测试文件
    std::filesystem::remove_all("./test_recordings");
    
    if (all_passed) {
        std::cout << "\nAll tests passed!" << std::endl;
        return 0;
    } else {
        std::cout << "\nSome tests failed!" << std::endl;
        return 1;
    }
}
