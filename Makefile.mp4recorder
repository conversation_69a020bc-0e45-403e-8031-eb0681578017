# MP4 Recorder Makefile

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++17 -Wall -O2 -g
LDFLAGS = -pthread

# 目录设置
SRC_DIR = .
INC_DIR = .
OBJ_DIR = obj
BIN_DIR = bin
LIB_DIR = lib

# 依赖的media-server和sdk路径
MEDIA_SERVER_DIR = ./media-server
SDK_DIR = ../sdk

# 包含路径
INCLUDES = -I$(INC_DIR) \
           -I$(MEDIA_SERVER_DIR)/libmov/include \
           -I$(MEDIA_SERVER_DIR)/libflv/include \
           -I$(MEDIA_SERVER_DIR)/librtmp/include \
           -I$(MEDIA_SERVER_DIR)/librtsp/include \
           -I$(MEDIA_SERVER_DIR)/libhls/include \
           -I$(MEDIA_SERVER_DIR)/librtp/include \
           -I$(MEDIA_SERVER_DIR)/libmpeg/include \
           -I$(SDK_DIR)/include \
           -I$(SDK_DIR)/libaio/include \
           -I$(SDK_DIR)/libhttp/include

# 库路径
LIBPATH = -L$(MEDIA_SERVER_DIR)/libmov \
          -L$(MEDIA_SERVER_DIR)/libflv \
          -L$(MEDIA_SERVER_DIR)/librtmp \
          -L$(MEDIA_SERVER_DIR)/librtsp \
          -L$(MEDIA_SERVER_DIR)/libhls \
          -L$(MEDIA_SERVER_DIR)/librtp \
          -L$(MEDIA_SERVER_DIR)/libmpeg \
          -L$(SDK_DIR)/libaio \
          -L$(SDK_DIR)/libhttp \
          -L$(LIB_DIR)

# 链接的库
LIBS = -lmov -lflv -lrtmp -lrtsp -lhls -lrtp -lmpeg -laio -lhttp \
       -lssl -lcrypto -ljsoncpp -lpthread -lrt

# 源文件
MP4_RECORDER_SRCS = mp4-recorder.cpp security-chip-sdk.cpp recording-manager.cpp
MP4_RECORDER_OBJS = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(MP4_RECORDER_SRCS))

EXAMPLE_SRCS = mp4-recorder-example.cpp
EXAMPLE_OBJS = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(EXAMPLE_SRCS))

TEST_SRCS = mp4-recorder-test.cpp
TEST_OBJS = $(patsubst %.cpp,$(OBJ_DIR)/%.o,$(TEST_SRCS))

# 目标文件
MP4_RECORDER_LIB = $(LIB_DIR)/libmp4recorder.a
EXAMPLE_TARGET = $(BIN_DIR)/mp4-recorder-example
TEST_TARGET = $(BIN_DIR)/mp4-recorder-test

# 默认目标
all: directories $(MP4_RECORDER_LIB) $(EXAMPLE_TARGET) $(TEST_TARGET)

# 创建目录
directories:
	@mkdir -p $(OBJ_DIR)
	@mkdir -p $(BIN_DIR)
	@mkdir -p $(LIB_DIR)
	@mkdir -p recordings
	@mkdir -p config

# 编译MP4录像器库
$(MP4_RECORDER_LIB): $(MP4_RECORDER_OBJS)
	@echo "Creating static library $@"
	ar rcs $@ $^

# 编译示例程序
$(EXAMPLE_TARGET): $(EXAMPLE_OBJS) $(MP4_RECORDER_LIB)
	@echo "Linking $@"
	$(CXX) $(LDFLAGS) -o $@ $^ $(LIBPATH) $(LIBS)

# 编译测试程序
$(TEST_TARGET): $(TEST_OBJS) $(MP4_RECORDER_LIB)
	@echo "Linking $@"
	$(CXX) $(LDFLAGS) -o $@ $^ $(LIBPATH) $(LIBS)

# 编译规则
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
	@echo "Compiling $<"
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -rf $(OBJ_DIR) $(BIN_DIR) $(LIB_DIR)

# 运行示例
run-example: $(EXAMPLE_TARGET)
	@echo "Running example..."
	$(EXAMPLE_TARGET)

# 运行测试
test: $(TEST_TARGET)
	@echo "Running tests..."
	$(TEST_TARGET)

# 检查依赖
check-deps:
	@echo "Checking dependencies..."
	@if [ ! -d "$(MEDIA_SERVER_DIR)" ]; then \
		echo "Error: media-server directory not found at $(MEDIA_SERVER_DIR)"; \
		echo "Please clone media-server repository"; \
		exit 1; \
	fi
	@if [ ! -d "$(SDK_DIR)" ]; then \
		echo "Error: sdk directory not found at $(SDK_DIR)"; \
		echo "Please clone sdk repository"; \
		exit 1; \
	fi
	@echo "Dependencies check passed"

# 构建依赖库
build-deps: check-deps
	@echo "Building dependencies..."
	@cd $(SDK_DIR) && make
	@cd $(MEDIA_SERVER_DIR) && make
	@echo "Dependencies built successfully"

# 创建配置文件
config:
	@if [ ! -f config/recording_config.json ]; then \
		cp config/recording_config.json.example config/recording_config.json; \
		echo "Created config/recording_config.json from template"; \
	else \
		echo "Config file already exists"; \
	fi

# 帮助信息
help:
	@echo "MP4 Recorder Makefile"
	@echo ""
	@echo "Available targets:"
	@echo "  all          - Build everything (default)"
	@echo "  clean        - Remove build files"
	@echo "  run-example  - Run example program"
	@echo "  test         - Run test program"
	@echo "  check-deps   - Check dependencies"
	@echo "  build-deps   - Build dependencies"
	@echo "  config       - Create config file from template"
	@echo "  help         - Show this help"

.PHONY: all clean run-example test check-deps build-deps config help directories
