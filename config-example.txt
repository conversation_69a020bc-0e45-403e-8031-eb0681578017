# MediaServer Configuration File

[server]
# Number of worker threads (0 = auto detect)
worker_threads = 0

# Media file root directory
media_root = ./media

[rtmp]
# RTMP server enable
enable = true

# RTMP server port
port = 1935

# Maximum connections
max_connections = 1000

# Chunk size
chunk_size = 4096

[rtsp]
# RTSP server enable
enable = true

# RTSP server port
port = 554

# RTP over UDP port range
rtp_port_start = 10000
rtp_port_end = 20000

# Session timeout (seconds)
session_timeout = 60

[http]
# HTTP server port
port = 8080

# HTTP server root directory
document_root = ./www

[hls]
# HLS enable
enable = true

# HLS URL path prefix
path_prefix = /hls

# Segment duration (seconds)
segment_duration = 10

# Number of segments in playlist
segment_count = 5

# Delete old segments
delete_old_segments = true

[httpflv]
# HTTP-FLV enable
enable = true

# HTTP-FLV URL path prefix
path_prefix = /flv

# Send buffer size
send_buffer_size = 65536

[log]
# Log level: debug, info, warn, error
level = info

# Log file path (empty = stdout)
file = 

# Log file max size (MB)
max_size = 100

# Log file max count
max_count = 10

[security]
# Enable authentication
auth_enable = false

# Authentication method: basic, digest
auth_method = basic

# User database file
user_db = users.db

[performance]
# GOP cache for live stream
gop_cache = true

# Maximum GOP cache size (packets)
gop_cache_size = 60

# Zero copy
zero_copy = true

# Send/Receive buffer size
socket_buffer_size = 1048576