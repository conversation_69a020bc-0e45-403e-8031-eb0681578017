#include "FileMediaSource.h"
#include "flv-reader.h"
#include "flv-demuxer.h"
#include "flv-proto.h"
#include <iostream>
#include <chrono>
#include <cstring>

// FLV解复用回调
static int OnFlvDemuxer(void* param, int codec, const void* data, size_t bytes, 
                        uint32_t pts, uint32_t dts, int flags) {
    FileMediaSource* source = (FileMediaSource*)param;
    
    MediaPacket packet;
    packet.timestamp = pts;
    
    switch (codec) {
    case FLV_VIDEO_H264:
        packet.type = MediaPacket::VIDEO_H264;
        packet.keyframe = (flags & FLV_VIDEO_KEY_FRAME) != 0;
        break;
        
    case FLV_VIDEO_H265:
        packet.type = MediaPacket::VIDEO_H265;
        packet.keyframe = (flags & FLV_VIDEO_KEY_FRAME) != 0;
        break;
        
    case FLV_AUDIO_AAC:
        packet.type = MediaPacket::AUDIO_AAC;
        break;
        
    case FLV_AUDIO_MP3:
    case FLV_AUDIO_G711A:
        packet.type = MediaPacket::AUDIO_G711A;
        break;
        
    case FLV_AUDIO_G711U:
        packet.type = MediaPacket::AUDIO_G711U;
        break;
        
    default:
        return 0;
    }
    
    packet.data.assign((const uint8_t*)data, (const uint8_t*)data + bytes);
    source->PushPacket(packet);
    
    return 0;
}

FileMediaSource::FileMediaSource(const std::string& path, const std::string& filePath)
    : m_path(path), m_filePath(filePath) {
    InitializeDemuxer();
}

FileMediaSource::~FileMediaSource() {
    m_running = false;
    m_playing = false;
    
    if (m_playThread.joinable()) {
        m_playThread.join();
    }
    
    CloseDemuxer();
}

void FileMediaSource::AddSink(std::weak_ptr<MediaSession> session) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 清理无效会话
    CleanupSessions();
    
    // 添加新会话
    m_sessions.push_back(session);
    
    // 发送媒体信息
    if (auto s = session.lock()) {
        s->OnMediaInfo(m_mediaInfo);
    }
    
    // 如果是第一个会话，开始播放
    if (m_sessions.size() == 1 && !m_playing) {
        Play();
    }
}

void FileMediaSource::RemoveSink(std::weak_ptr<MediaSession> session) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_sessions.remove_if([&session](const std::weak_ptr<MediaSession>& s) {
        auto s1 = s.lock();
        auto s2 = session.lock();
        return !s1 || !s2 || s1 == s2;
    });
    
    // 如果没有会话了，暂停播放
    if (m_sessions.empty()) {
        Pause();
    }
}

void FileMediaSource::PushPacket(const MediaPacket& packet) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_currentTimestamp = packet.timestamp;
    
    // 清理无效会话
    CleanupSessions();
    
    // 分发数据包
    for (auto& weakSession : m_sessions) {
        if (auto session = weakSession.lock()) {
            session->OnMediaPacket(packet);
        }
    }
}

void FileMediaSource::Play() {
    if (m_playing) {
        return;
    }
    
    m_playing = true;
    
    if (!m_running) {
        m_running = true;
        m_playThread = std::thread(&FileMediaSource::PlayThread, this);
    }
}

void FileMediaSource::Pause() {
    m_playing = false;
}

void FileMediaSource::Seek(uint32_t timestamp) {
    // TODO: 实现文件定位
    m_currentTimestamp = timestamp;
}

void FileMediaSource::InitializeDemuxer() {
    // 简单判断文件类型，这里只支持FLV
    if (m_filePath.find(".flv") != std::string::npos ||
        m_filePath.find(".FLV") != std::string::npos) {
        
        // 打开FLV文件
        void* flv = flv_reader_create(m_filePath.c_str());
        if (!flv) {
            std::cerr << "Failed to open FLV file: " << m_filePath << std::endl;
            return;
        }
        
        // 创建FLV解复用器
        m_demuxer = flv_demuxer_create(OnFlvDemuxer, this);
        
        // 读取文件头，获取媒体信息
        uint8_t buffer[1024 * 1024];
        int type;
        uint32_t timestamp;
        size_t taglen;
        
        // 读取几个包来获取媒体信息
        for (int i = 0; i < 10 && flv_reader_read(flv, &type, &timestamp, &taglen, buffer, sizeof(buffer)) == 1; i++) {
            flv_demuxer_input((flv_demuxer_t*)m_demuxer, type, buffer, taglen, timestamp);
            
            // 简单检测媒体类型
            if (type == FLV_TYPE_VIDEO && !m_mediaInfo.has_video) {
                m_mediaInfo.has_video = true;
                // 判断视频编码
                if (buffer[0] == 0x17 || buffer[0] == 0x27) {
                    m_mediaInfo.video_codec = 1; // H264
                } else if (buffer[0] == 0x1C || buffer[0] == 0x2C) {
                    m_mediaInfo.video_codec = 2; // H265
                }
            } else if (type == FLV_TYPE_AUDIO && !m_mediaInfo.has_audio) {
                m_mediaInfo.has_audio = true;
                // 判断音频编码
                int soundFormat = (buffer[0] >> 4) & 0x0F;
                if (soundFormat == FLV_AUDIO_AAC) {
                    m_mediaInfo.audio_codec = 1; // AAC
                } else if (soundFormat == FLV_AUDIO_G711A) {
                    m_mediaInfo.audio_codec = 2; // G711A
                } else if (soundFormat == FLV_AUDIO_G711U) {
                    m_mediaInfo.audio_codec = 3; // G711U
                }
            }
        }
        
        flv_reader_destroy(flv);
        
        // 重置解复用器
        if (m_demuxer) {
            flv_demuxer_destroy((flv_demuxer_t*)m_demuxer);
            m_demuxer = nullptr;
        }
    } else {
        std::cerr << "Unsupported file format: " << m_filePath << std::endl;
    }
}

void FileMediaSource::CloseDemuxer() {
    if (m_demuxer) {
        flv_demuxer_destroy((flv_demuxer_t*)m_demuxer);
        m_demuxer = nullptr;
    }
}

void FileMediaSource::PlayThread() {
    // 重新打开文件
    void* flv = flv_reader_create(m_filePath.c_str());
    if (!flv) {
        std::cerr << "Failed to open FLV file for playback: " << m_filePath << std::endl;
        return;
    }
    
    // 创建解复用器
    flv_demuxer_t* demuxer = flv_demuxer_create(OnFlvDemuxer, this);
    
    uint8_t buffer[2 * 1024 * 1024];
    int type;
    uint32_t timestamp, last_timestamp = 0;
    size_t taglen;
    
    auto start_time = std::chrono::steady_clock::now();
    
    while (m_running) {
        if (!m_playing) {
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            continue;
        }
        
        // 读取一个FLV标签
        if (flv_reader_read(flv, &type, &timestamp, &taglen, buffer, sizeof(buffer)) != 1) {
            // 文件结束，循环播放
            flv_reader_destroy(flv);
            flv = flv_reader_create(m_filePath.c_str());
            if (!flv) {
                break;
            }
            last_timestamp = 0;
            start_time = std::chrono::steady_clock::now();
            continue;
        }
        
        // 控制播放速度
        if (timestamp > last_timestamp) {
            auto elapsed = std::chrono::steady_clock::now() - start_time;
            auto target = std::chrono::milliseconds(timestamp);
            
            if (target > elapsed) {
                std::this_thread::sleep_for(target - elapsed);
            }
        }
        last_timestamp = timestamp;
        
        // 解复用
        flv_demuxer_input(demuxer, type, buffer, taglen, timestamp);
    }
    
    flv_demuxer_destroy(demuxer);
    flv_reader_destroy(flv);
}

void FileMediaSource::CleanupSessions() {
    m_sessions.remove_if([](const std::weak_ptr<MediaSession>& s) {
        return s.expired();
    });
}