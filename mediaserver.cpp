#include "MediaServer.h"
#include "RtmpServer.h"
#include "RtspServer.h"
#include "HlsServer.h"
#include "HttpFlvServer.h"
#include "MediaSourceManager.h"
#include <iostream>

MediaServer::MediaServer(std::shared_ptr<MediaSourceManager> sourceManager)
    : m_sourceManager(sourceManager) {
}

MediaServer::~MediaServer() {
    Stop();
}

bool MediaServer::Start(const MediaServerConfig& config) {
    if (m_running) {
        return false;
    }
    
    m_config = config;
    
    try {
        // 创建RTMP服务器
        m_rtmpServer = std::make_unique<RtmpServer>(m_sourceManager);
        if (!m_rtmpServer->Start(config.rtmp_port)) {
            std::cerr << "Failed to start RTMP server" << std::endl;
            return false;
        }
        
        // 创建RTSP服务器
        m_rtspServer = std::make_unique<RtspServer>(m_sourceManager);
        if (!m_rtspServer->Start(config.rtsp_port)) {
            std::cerr << "Failed to start RTSP server" << std::endl;
            return false;
        }
        
        // 创建HLS服务器
        m_hlsServer = std::make_unique<HlsServer>(m_sourceManager);
        if (!m_hlsServer->Start(config.http_port, "/hls")) {
            std::cerr << "Failed to start HLS server" << std::endl;
            return false;
        }
        
        // 创建HTTP-FLV服务器
        m_httpFlvServer = std::make_unique<HttpFlvServer>(m_sourceManager);
        if (!m_httpFlvServer->Start(config.http_port, "/flv")) {
            std::cerr << "Failed to start HTTP-FLV server" << std::endl;
            return false;
        }
        
        // 启动工作线程
        m_running = true;
        for (int i = 0; i < config.worker_threads; ++i) {
            m_workers.emplace_back(&MediaServer::WorkerThread, this);
        }
        
        return true;
    }
    catch (const std::exception& e) {
        std::cerr << "Exception during server start: " << e.what() << std::endl;
        Stop();
        return false;
    }
}

void MediaServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    // 停止所有服务器
    if (m_rtmpServer) {
        m_rtmpServer->Stop();
    }
    if (m_rtspServer) {
        m_rtspServer->Stop();
    }
    if (m_hlsServer) {
        m_hlsServer->Stop();
    }
    if (m_httpFlvServer) {
        m_httpFlvServer->Stop();
    }
    
    // 等待工作线程结束
    for (auto& thread : m_workers) {
        if (thread.joinable()) {
            thread.join();
        }
    }
    m_workers.clear();
}

void MediaServer::WorkerThread() {
    while (m_running) {
        // 处理媒体数据
        m_sourceManager->ProcessMediaData();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}