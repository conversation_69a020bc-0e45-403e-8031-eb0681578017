#ifndef HTTP_FLV_SERVER_H
#define HTTP_FLV_SERVER_H

#include <memory>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>
#include <queue>
#include "MediaSourceManager.h"
#include "http-server.h"
#include "flv-muxer.h"

// HTTP-FLV会话
class HttpFlvSession : public MediaSession, public std::enable_shared_from_this<HttpFlvSession> {
public:
    HttpFlvSession(http_session_t* session, const std::string& path, 
                   std::shared_ptr<MediaSourceManager> sourceManager);
    ~HttpFlvSession();
    
    void Start();
    void Stop();
    
    // MediaSession接口
    void OnMediaPacket(const MediaPacket& packet) override;
    void OnMediaInfo(const MediaInfo& info) override;
    
private:
    http_session_t* m_httpSession;
    std::string m_path;
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::shared_ptr<MediaSource> m_mediaSource;
    std::atomic<bool> m_running{false};
    
    // FLV封装
    flv_muxer_t* m_flv;
    MediaInfo m_mediaInfo;
    bool m_headerSent = false;
    bool m_metadataSent = false;
    bool m_videoConfigSent = false;
    bool m_audioConfigSent = false;
    
    // 数据队列
    std::mutex m_queueMutex;
    std::queue<std::vector<uint8_t>> m_dataQueue;
    std::condition_variable m_queueCond;
    
    // FLV回调
    static int OnFlvPacket(void* param, int type, const void* data, size_t bytes, uint32_t timestamp);
    
    void SendFlvHeader();
    void SendMetadata();
    void SendVideoConfig();
    void SendAudioConfig();
    void SendData(const void* data, size_t bytes);
    
    // HTTP发送回调
    static int OnHttpSend(void* param, int code, size_t bytes);
};

// HTTP-FLV服务器
class HttpFlvServer {
public:
    HttpFlvServer(std::shared_ptr<MediaSourceManager> sourceManager);
    ~HttpFlvServer();
    
    bool Start(int port, const std::string& path);
    void Stop();
    
private:
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::atomic<bool> m_running{false};
    http_server_t* m_httpServer;
    std::string m_path;
    
    // 会话管理
    mutable std::mutex m_sessionMutex;
    std::map<http_session_t*, std::shared_ptr<HttpFlvSession>> m_sessions;
    
    // HTTP请求处理
    static int OnHttpRequest(void* param, http_session_t* session, 
                            const char* method, const char* uri);
    
    int HandleFlvRequest(http_session_t* session, const std::string& path);
    void RemoveSession(http_session_t* session);
};

#endif // HTTP_FLV_SERVER_H