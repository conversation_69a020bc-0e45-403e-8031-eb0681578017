#include "LiveMediaSource.h"
#include <algorithm>
#include <iostream>

LiveMediaSource::LiveMediaSource(const std::string& path)
    : m_path(path), m_startTime(std::chrono::steady_clock::now()) {
}

LiveMediaSource::~LiveMediaSource() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_sessions.clear();
    m_gopCache.clear();
}

void LiveMediaSource::AddSink(std::weak_ptr<MediaSession> session) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 清理无效会话
    CleanupSessions();
    
    // 添加新会话
    m_sessions.push_back(session);
    
    // 发送媒体信息
    if (auto s = session.lock()) {
        s->OnMediaInfo(m_mediaInfo);
        
        // 发送GOP缓存
        SendGOPCache(session);
    }
}

void LiveMediaSource::RemoveSink(std::weak_ptr<MediaSession> session) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_sessions.remove_if([&session](const std::weak_ptr<MediaSession>& s) {
        auto s1 = s.lock();
        auto s2 = session.lock();
        return !s1 || !s2 || s1 == s2;
    });
}

void LiveMediaSource::PushPacket(const MediaPacket& packet) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    // 更新统计信息
    m_packetCount++;
    m_byteCount += packet.data.size();
    
    // 更新GOP缓存
    UpdateGOPCache(packet);
    
    // 清理无效会话
    CleanupSessions();
    
    // 分发数据包
    for (auto& weakSession : m_sessions) {
        if (auto session = weakSession.lock()) {
            session->OnMediaPacket(packet);
        }
    }
}

void LiveMediaSource::SetMediaInfo(const MediaInfo& info) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    m_mediaInfo = info;
    
    // 通知所有会话
    for (auto& weakSession : m_sessions) {
        if (auto session = weakSession.lock()) {
            session->OnMediaInfo(info);
        }
    }
}

void LiveMediaSource::CleanupSessions() {
    m_sessions.remove_if([](const std::weak_ptr<MediaSession>& s) {
        return s.expired();
    });
}

void LiveMediaSource::UpdateGOPCache(const MediaPacket& packet) {
    // 只缓存视频包
    if (packet.type != MediaPacket::VIDEO_H264 && 
        packet.type != MediaPacket::VIDEO_H265) {
        return;
    }
    
    // 如果是关键帧，清空缓存重新开始
    if (packet.keyframe) {
        m_gopCache.clear();
        m_hasKeyframe = true;
    }
    
    // 只有在有关键帧的情况下才缓存
    if (m_hasKeyframe) {
        m_gopCache.push_back(packet);
        
        // 限制缓存大小（保留最近2个GOP）
        while (m_gopCache.size() > 60) { // 假设30fps，2秒的数据
            m_gopCache.pop_front();
        }
    }
}

void LiveMediaSource::SendGOPCache(std::weak_ptr<MediaSession> session) {
    if (!m_hasKeyframe || m_gopCache.empty()) {
        return;
    }
    
    if (auto s = session.lock()) {
        for (const auto& packet : m_gopCache) {
            s->OnMediaPacket(packet);
        }
    }
}