#include "HttpFlvServer.h"
#include "flv-proto.h"
#include "flv-header.h"
#include "aio-worker.h"
#include <iostream>
#include <cstring>

// HttpFlvSession实现
HttpFlvSession::HttpFlvSession(http_session_t* session, const std::string& path,
                               std::shared_ptr<MediaSourceManager> sourceManager)
    : m_httpSession(session), m_path(path), m_sourceManager(sourceManager), m_flv(nullptr) {
}

HttpFlvSession::~HttpFlvSession() {
    Stop();
}

void HttpFlvSession::Start() {
    if (m_running) {
        return;
    }
    
    m_running = true;
    
    // 查找媒体源
    m_mediaSource = m_sourceManager->FindSource(m_path);
    if (!m_mediaSource) {
        // 创建文件源或等待推流
        m_mediaSource = m_sourceManager->CreateOrGetSource(m_path, true);
    }
    
    if (!m_mediaSource) {
        Stop();
        return;
    }
    
    // 创建FLV封装器
    m_flv = flv_muxer_create(OnFlvPacket, this);
    if (!m_flv) {
        Stop();
        return;
    }
    
    // 发送HTTP响应头
    http_server_set_header(m_httpSession, "Content-Type", "video/x-flv");
    http_server_set_header(m_httpSession, "Cache-Control", "no-cache");
    http_server_set_header(m_httpSession, "Connection", "close");
    http_server_set_header(m_httpSession, "Expires", "-1");
    http_server_set_header(m_httpSession, "Pragma", "no-cache");
    
    // 发送FLV文件头
    SendFlvHeader();
    
    // 获取媒体信息
    m_mediaInfo = m_mediaSource->GetMediaInfo();
    
    // 发送元数据
    SendMetadata();
    
    // 发送配置信息
    if (m_mediaInfo.has_video) {
        SendVideoConfig();
    }
    if (m_mediaInfo.has_audio) {
        SendAudioConfig();
    }
    
    // 添加到媒体源
    m_mediaSource->AddSink(weak_from_this());
}

void HttpFlvSession::Stop() {
    m_running = false;
    
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
        m_mediaSource.reset();
    }
    
    if (m_flv) {
        flv_muxer_destroy(m_flv);
        m_flv = nullptr;
    }
    
    // 清空队列
    {
        std::lock_guard<std::mutex> lock(m_queueMutex);
        while (!m_dataQueue.empty()) {
            m_dataQueue.pop();
        }
    }
}

void HttpFlvSession::OnMediaPacket(const MediaPacket& packet) {
    if (!m_running || !m_flv) {
        return;
    }
    
    int codec = 0;
    int flags = 0;
    
    switch (packet.type) {
    case MediaPacket::VIDEO_H264:
        codec = FLV_VIDEO_H264;
        flags = packet.keyframe ? 1 : 0;
        break;
        
    case MediaPacket::VIDEO_H265:
        codec = FLV_VIDEO_H265;
        flags = packet.keyframe ? 1 : 0;
        break;
        
    case MediaPacket::AUDIO_AAC:
        codec = FLV_AUDIO_AAC;
        break;
        
    case MediaPacket::AUDIO_G711A:
        codec = FLV_AUDIO_G711A;
        break;
        
    case MediaPacket::AUDIO_G711U:
        codec = FLV_AUDIO_G711U;
        break;
        
    default:
        return;
    }
    
    flv_muxer_input(m_flv, codec, packet.data.data(), packet.data.size(), 
                    packet.timestamp, packet.timestamp, flags);
}

void HttpFlvSession::OnMediaInfo(const MediaInfo& info) {
    m_mediaInfo = info;
    
    if (!m_metadataSent) {
        SendMetadata();
    }
    
    if (!m_videoConfigSent && info.has_video) {
        SendVideoConfig();
    }
    
    if (!m_audioConfigSent && info.has_audio) {
        SendAudioConfig();
    }
}

int HttpFlvSession::OnFlvPacket(void* param, int type, const void* data, size_t bytes, uint32_t timestamp) {
    HttpFlvSession* session = (HttpFlvSession*)param;
    
    std::vector<uint8_t> packet(bytes);
    memcpy(packet.data(), data, bytes);
    
    session->SendData(packet.data(), packet.size());
    
    return 0;
}

void HttpFlvSession::SendFlvHeader() {
    struct flv_header_t header;
    header.FLV[0] = 'F';
    header.FLV[1] = 'L';
    header.FLV[2] = 'V';
    header.version = 1;
    header.audio = m_mediaInfo.has_audio ? 1 : 0;
    header.video = m_mediaInfo.has_video ? 1 : 0;
    header.offset = 9;
    
    uint8_t buffer[13]; // FLV header + previous tag size
    int n = flv_header_write(&header, buffer, sizeof(buffer));
    
    // Previous tag size (always 0 for first tag)
    buffer[n++] = 0;
    buffer[n++] = 0;
    buffer[n++] = 0;
    buffer[n++] = 0;
    
    SendData(buffer, n);
    m_headerSent = true;
}

void HttpFlvSession::SendMetadata() {
    if (m_metadataSent || !m_flv) {
        return;
    }
    
    // TODO: 创建onMetaData
    uint8_t metadata[1024];
    int metadataSize = 0;
    
    // 简单的metadata示例
    const char* script = "{\"duration\":0,\"width\":%d,\"height\":%d,\"videocodecid\":%d,\"audiocodecid\":%d}";
    char buffer[512];
    snprintf(buffer, sizeof(buffer), script, 
             m_mediaInfo.video_width, m_mediaInfo.video_height,
             m_mediaInfo.video_codec == 1 ? FLV_VIDEO_H264 : FLV_VIDEO_H265,
             m_mediaInfo.audio_codec == 1 ? FLV_AUDIO_AAC : FLV_AUDIO_MP3);
    
    // TODO: 使用AMF0编码metadata
    
    m_metadataSent = true;
}

void HttpFlvSession::SendVideoConfig() {
    if (m_videoConfigSent || !m_flv || !m_mediaInfo.has_video) {
        return;
    }
    
    // TODO: 发送视频配置
    if (m_mediaInfo.video_codec == 1) { // H264
        // 发送AVC sequence header
        if (!m_mediaInfo.video_sps.empty() && !m_mediaInfo.video_pps.empty()) {
            // TODO: 构造AVC配置
        }
    } else if (m_mediaInfo.video_codec == 2) { // H265
        // 发送HEVC sequence header
        // TODO: 构造HEVC配置
    }
    
    m_videoConfigSent = true;
}

void HttpFlvSession::SendAudioConfig() {
    if (m_audioConfigSent || !m_flv || !m_mediaInfo.has_audio) {
        return;
    }
    
    // TODO: 发送音频配置
    if (m_mediaInfo.audio_codec == 1) { // AAC
        // 发送AAC sequence header
        if (!m_mediaInfo.audio_config.empty()) {
            // TODO: 构造AAC配置
        }
    }
    
    m_audioConfigSent = true;
}

void HttpFlvSession::SendData(const void* data, size_t bytes) {
    if (!m_running || !m_httpSession) {
        return;
    }
    
    // 直接发送数据
    http_server_send(m_httpSession, data, bytes, OnHttpSend, this);
}

int HttpFlvSession::OnHttpSend(void* param, int code, size_t bytes) {
    HttpFlvSession* session = (HttpFlvSession*)param;
    
    if (code != 0) {
        // 发送失败，停止会话
        session->Stop();
    }
    
    return 0;
}

// HttpFlvServer实现
HttpFlvServer::HttpFlvServer(std::shared_ptr<MediaSourceManager> sourceManager)
    : m_sourceManager(sourceManager), m_httpServer(nullptr) {
    aio_worker_init(4);
}

HttpFlvServer::~HttpFlvServer() {
    Stop();
    aio_worker_clean(4);
}

bool HttpFlvServer::Start(int port, const std::string& path) {
    if (m_running) {
        return false;
    }
    
    m_path = path;
    
    // 创建HTTP服务器
    m_httpServer = http_server_create(NULL, port);
    if (!m_httpServer) {
        std::cerr << "Failed to create HTTP server on port " << port << std::endl;
        return false;
    }
    
    // 设置HTTP请求处理器
    http_server_set_handler(m_httpServer, OnHttpRequest, this);
    
    m_running = true;
    return true;
}

void HttpFlvServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    // 停止所有会话
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (auto& pair : m_sessions) {
            pair.second->Stop();
        }
        m_sessions.clear();
    }
    
    if (m_httpServer) {
        http_server_destroy(m_httpServer);
        m_httpServer = nullptr;
    }
}

int HttpFlvServer::OnHttpRequest(void* param, http_session_t* session,
                                 const char* method, const char* uri) {
    HttpFlvServer* server = (HttpFlvServer*)param;
    
    if (strcmp(method, "GET") != 0) {
        http_server_set_status_code(session, 405, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    std::string path(uri);
    
    // 检查是否是FLV请求
    if (path.find(server->m_path) != 0) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    // 移除前缀
    path = path.substr(server->m_path.length());
    if (!path.empty() && path[0] == '/') {
        path = path.substr(1);
    }
    
    // 检查文件扩展名
    if (path.find(".flv") == std::string::npos) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    // 移除.flv扩展名
    path = path.substr(0, path.find(".flv"));
    
    return server->HandleFlvRequest(session, path);
}

int HttpFlvServer::HandleFlvRequest(http_session_t* session, const std::string& path) {
    // 创建新会话
    auto flvSession = std::make_shared<HttpFlvSession>(session, path, m_sourceManager);
    
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_sessions[session] = flvSession;
    }
    
    // 启动会话
    flvSession->Start();
    
    return 0;
}

void HttpFlvServer::RemoveSession(http_session_t* session) {
    std::lock_guard<std::mutex> lock(m_sessionMutex);
    
    auto it = m_sessions.find(session);
    if (it != m_sessions.end()) {
        it->second->Stop();
        m_sessions.erase(it);
    }
}