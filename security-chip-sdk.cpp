#include "security-chip-sdk.h"
#include <iostream>
#include <chrono>
#include <cstring>

// 基础SecurityChipSDK实现
SecurityChipSDK::SecurityChipSDK(const SecurityChipConfig& config)
    : m_config(config) {
}

SecurityChipSDK::~SecurityChipSDK() {
    Disconnect();
}

bool SecurityChipSDK::Connect() {
    if (m_connected) {
        return true;
    }
    
    if (ConnectDevice()) {
        m_connected = true;
        if (m_status_callback) {
            m_status_callback(true, "Connected successfully");
        }
        return true;
    }
    
    if (m_status_callback) {
        m_status_callback(false, "Failed to connect to device");
    }
    return false;
}

bool SecurityChipSDK::Disconnect() {
    if (!m_connected) {
        return true;
    }
    
    StopStream();
    
    if (DisconnectDevice()) {
        m_connected = false;
        if (m_status_callback) {
            m_status_callback(false, "Disconnected");
        }
        return true;
    }
    
    return false;
}

bool SecurityChipSDK::StartStream() {
    if (!m_connected || m_streaming) {
        return false;
    }
    
    // 启动接收线程
    m_running = true;
    m_receive_thread = std::thread(&SecurityChipSDK::ReceiveThread, this);
    
    // 启动视频流
    if (!StartVideoStream()) {
        m_running = false;
        if (m_receive_thread.joinable()) {
            m_receive_thread.join();
        }
        return false;
    }
    
    // 启动音频流（如果启用）
    if (m_config.enable_audio) {
        if (!StartAudioStream()) {
            StopVideoStream();
            m_running = false;
            if (m_receive_thread.joinable()) {
                m_receive_thread.join();
            }
            return false;
        }
    }
    
    m_streaming = true;
    return true;
}

bool SecurityChipSDK::StopStream() {
    if (!m_streaming) {
        return true;
    }
    
    // 停止流
    StopVideoStream();
    if (m_config.enable_audio) {
        StopAudioStream();
    }
    
    // 停止接收线程
    m_running = false;
    if (m_receive_thread.joinable()) {
        m_receive_thread.join();
    }
    
    m_streaming = false;
    return true;
}

bool SecurityChipSDK::GetVideoParams(int& width, int& height, int& fps,
                                    std::vector<uint8_t>& sps, std::vector<uint8_t>& pps) {
    width = m_config.video_width;
    height = m_config.video_height;
    fps = m_config.video_fps;
    sps = m_video_sps;
    pps = m_video_pps;
    return !sps.empty() && !pps.empty();
}

bool SecurityChipSDK::GetAudioParams(int& channels, int& sample_rate, int& bits_per_sample,
                                    std::vector<uint8_t>& config) {
    channels = m_config.audio_channels;
    sample_rate = m_config.audio_sample_rate;
    bits_per_sample = m_config.audio_bits_per_sample;
    config = m_audio_config;
    return !config.empty();
}

bool SecurityChipSDK::SetVideoParams(int width, int height, int fps, int bitrate) {
    m_config.video_width = width;
    m_config.video_height = height;
    m_config.video_fps = fps;
    m_config.video_bitrate = bitrate;
    return true;
}

bool SecurityChipSDK::SetAudioParams(int sample_rate, int channels, int bits_per_sample) {
    m_config.audio_sample_rate = sample_rate;
    m_config.audio_channels = channels;
    m_config.audio_bits_per_sample = bits_per_sample;
    return true;
}

std::string SecurityChipSDK::GetDeviceInfo() const {
    return "Generic Security Chip Device";
}

std::string SecurityChipSDK::GetFirmwareVersion() const {
    return "Unknown";
}

void SecurityChipSDK::ReceiveThread() {
    while (m_running) {
        // 处理缓冲区中的数据
        std::vector<uint8_t> video_data;
        std::vector<uint8_t> audio_data;
        
        {
            std::lock_guard<std::mutex> lock(m_buffer_mutex);
            if (!m_video_buffer.empty()) {
                video_data = m_video_buffer.front();
                m_video_buffer.pop();
            }
            if (!m_audio_buffer.empty()) {
                audio_data = m_audio_buffer.front();
                m_audio_buffer.pop();
            }
        }
        
        // 处理视频数据
        if (!video_data.empty()) {
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count();
            ProcessVideoData(video_data.data(), video_data.size(), timestamp);
        }
        
        // 处理音频数据
        if (!audio_data.empty()) {
            auto timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                std::chrono::steady_clock::now().time_since_epoch()).count();
            ProcessAudioData(audio_data.data(), audio_data.size(), timestamp);
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void SecurityChipSDK::ProcessVideoData(const uint8_t* data, size_t size, int64_t timestamp) {
    if (!m_stream_callback) {
        return;
    }
    
    // 解析H.264 NALU
    std::vector<MediaFrame> frames;
    if (ParseH264NALU(data, size, frames)) {
        for (auto& frame : frames) {
            frame.pts = timestamp;
            frame.dts = timestamp;
            m_stream_callback(frame);
        }
    }
}

void SecurityChipSDK::ProcessAudioData(const uint8_t* data, size_t size, int64_t timestamp) {
    if (!m_stream_callback) {
        return;
    }
    
    // 解析AAC帧
    MediaFrame frame;
    if (ParseAACFrame(data, size, frame)) {
        frame.pts = timestamp;
        frame.dts = timestamp;
        m_stream_callback(frame);
    }
}

bool SecurityChipSDK::ParseH264NALU(const uint8_t* data, size_t size, std::vector<MediaFrame>& frames) {
    // 简化的H.264 NALU解析实现
    // 实际使用时需要完整的H.264解析器
    
    const uint8_t* ptr = data;
    const uint8_t* end = data + size;
    
    while (ptr < end - 4) {
        // 查找起始码 0x00000001 或 0x000001
        if (ptr[0] == 0x00 && ptr[1] == 0x00) {
            if (ptr[2] == 0x00 && ptr[3] == 0x01) {
                // 找到4字节起始码
                ptr += 4;
            } else if (ptr[2] == 0x01) {
                // 找到3字节起始码
                ptr += 3;
            } else {
                ptr++;
                continue;
            }
            
            // 解析NALU头
            if (ptr >= end) break;
            
            uint8_t nalu_type = ptr[0] & 0x1F;
            
            // 查找下一个起始码
            const uint8_t* next_start = ptr + 1;
            while (next_start < end - 3) {
                if (next_start[0] == 0x00 && next_start[1] == 0x00 &&
                    (next_start[2] == 0x01 || (next_start[2] == 0x00 && next_start[3] == 0x01))) {
                    break;
                }
                next_start++;
            }
            
            size_t nalu_size = next_start - ptr;
            
            // 处理不同类型的NALU
            if (nalu_type == 7) { // SPS
                ParseSPS(ptr, nalu_size);
            } else if (nalu_type == 8) { // PPS
                ParsePPS(ptr, nalu_size);
            } else if (nalu_type == 5 || nalu_type == 1) { // IDR或非IDR帧
                MediaFrame frame;
                frame.type = MediaFrame::VIDEO_H264;
                frame.data.assign(ptr, ptr + nalu_size);
                frame.keyframe = (nalu_type == 5);
                frames.push_back(frame);
            }
            
            ptr = next_start;
        } else {
            ptr++;
        }
    }
    
    return !frames.empty();
}

bool SecurityChipSDK::ParseSPS(const uint8_t* data, size_t size) {
    m_video_sps.assign(data, data + size);
    return true;
}

bool SecurityChipSDK::ParsePPS(const uint8_t* data, size_t size) {
    m_video_pps.assign(data, data + size);
    return true;
}

bool SecurityChipSDK::ParseAACFrame(const uint8_t* data, size_t size, MediaFrame& frame) {
    // 简化的AAC帧解析
    // 实际使用时需要完整的AAC解析器
    
    frame.type = MediaFrame::AUDIO_AAC;
    frame.data.assign(data, data + size);
    frame.keyframe = false; // 音频帧没有关键帧概念

    return true;
}

// 海康威视SDK实现
HikvisionSDK::HikvisionSDK(const SecurityChipConfig& config)
    : SecurityChipSDK(config)
    , m_login_handle(nullptr)
    , m_video_handle(nullptr)
    , m_audio_handle(nullptr) {
}

HikvisionSDK::~HikvisionSDK() {
    Disconnect();
}

bool HikvisionSDK::ConnectDevice() {
    // 这里应该调用海康威视SDK的登录函数
    // 示例代码，实际需要链接海康SDK库
    /*
    NET_DVR_DEVICEINFO_V30 device_info;
    m_login_handle = NET_DVR_Login_V30(
        const_cast<char*>(m_config.ip_address.c_str()),
        m_config.port,
        const_cast<char*>(m_config.username.c_str()),
        const_cast<char*>(m_config.password.c_str()),
        &device_info
    );

    return m_login_handle != nullptr;
    */

    // 模拟连接成功
    m_login_handle = (void*)0x12345678;
    return true;
}

bool HikvisionSDK::DisconnectDevice() {
    if (m_login_handle) {
        // NET_DVR_Logout(m_login_handle);
        m_login_handle = nullptr;
    }
    return true;
}

bool HikvisionSDK::StartVideoStream() {
    if (!m_login_handle) {
        return false;
    }

    // 这里应该调用海康威视SDK的预览函数
    /*
    NET_DVR_PREVIEWINFO preview_info = {0};
    preview_info.hPlayWnd = nullptr;
    preview_info.lChannel = m_config.video_channel;
    preview_info.dwStreamType = m_config.video_stream;
    preview_info.dwLinkMode = 0;
    preview_info.bBlocked = 1;

    m_video_handle = NET_DVR_RealPlay_V40(
        m_login_handle,
        &preview_info,
        VideoDataCallback,
        this
    );

    return m_video_handle != nullptr;
    */

    // 模拟启动成功
    m_video_handle = (void*)0x87654321;
    return true;
}

bool HikvisionSDK::StartAudioStream() {
    // 海康威视的音频通常与视频一起传输
    // 或者需要单独的音频接口
    m_audio_handle = (void*)0x11111111;
    return true;
}

bool HikvisionSDK::StopVideoStream() {
    if (m_video_handle) {
        // NET_DVR_StopRealPlay(m_video_handle);
        m_video_handle = nullptr;
    }
    return true;
}

bool HikvisionSDK::StopAudioStream() {
    if (m_audio_handle) {
        m_audio_handle = nullptr;
    }
    return true;
}

void HikvisionSDK::VideoDataCallback(int lRealHandle, unsigned int dwDataType,
                                    unsigned char* pBuffer, unsigned int dwBufSize, void* pUser) {
    HikvisionSDK* sdk = static_cast<HikvisionSDK*>(pUser);
    if (sdk && pBuffer && dwBufSize > 0) {
        std::lock_guard<std::mutex> lock(sdk->m_buffer_mutex);
        std::vector<uint8_t> data(pBuffer, pBuffer + dwBufSize);
        sdk->m_video_buffer.push(data);
    }
}

void HikvisionSDK::AudioDataCallback(int lRealHandle, unsigned int dwDataType,
                                    unsigned char* pBuffer, unsigned int dwBufSize, void* pUser) {
    HikvisionSDK* sdk = static_cast<HikvisionSDK*>(pUser);
    if (sdk && pBuffer && dwBufSize > 0) {
        std::lock_guard<std::mutex> lock(sdk->m_buffer_mutex);
        std::vector<uint8_t> data(pBuffer, pBuffer + dwBufSize);
        sdk->m_audio_buffer.push(data);
    }
}

// 大华SDK实现
DahuaSDK::DahuaSDK(const SecurityChipConfig& config)
    : SecurityChipSDK(config)
    , m_login_handle(nullptr)
    , m_video_handle(nullptr)
    , m_audio_handle(nullptr) {
}

DahuaSDK::~DahuaSDK() {
    Disconnect();
}

bool DahuaSDK::ConnectDevice() {
    // 大华SDK登录实现
    /*
    NET_DEVICEINFO device_info;
    m_login_handle = CLIENT_Login(
        const_cast<char*>(m_config.ip_address.c_str()),
        m_config.port,
        const_cast<char*>(m_config.username.c_str()),
        const_cast<char*>(m_config.password.c_str()),
        &device_info,
        nullptr
    );

    return m_login_handle != nullptr;
    */

    m_login_handle = (void*)0x22222222;
    return true;
}

bool DahuaSDK::DisconnectDevice() {
    if (m_login_handle) {
        // CLIENT_Logout(m_login_handle);
        m_login_handle = nullptr;
    }
    return true;
}

bool DahuaSDK::StartVideoStream() {
    if (!m_login_handle) {
        return false;
    }

    /*
    m_video_handle = CLIENT_RealPlayEx(
        m_login_handle,
        m_config.video_channel,
        nullptr,
        DH_RType_Realplay
    );

    CLIENT_SetRealDataCallBack(m_video_handle, VideoDataCallback, this);
    return m_video_handle != nullptr;
    */

    m_video_handle = (void*)0x33333333;
    return true;
}

bool DahuaSDK::StartAudioStream() {
    m_audio_handle = (void*)0x44444444;
    return true;
}

bool DahuaSDK::StopVideoStream() {
    if (m_video_handle) {
        // CLIENT_StopRealPlay(m_video_handle);
        m_video_handle = nullptr;
    }
    return true;
}

bool DahuaSDK::StopAudioStream() {
    if (m_audio_handle) {
        m_audio_handle = nullptr;
    }
    return true;
}

int DahuaSDK::VideoDataCallback(int lRealHandle, int dwDataType,
                               unsigned char* pBuffer, int dwBufSize, void* dwUser) {
    DahuaSDK* sdk = static_cast<DahuaSDK*>(dwUser);
    if (sdk && pBuffer && dwBufSize > 0) {
        std::lock_guard<std::mutex> lock(sdk->m_buffer_mutex);
        std::vector<uint8_t> data(pBuffer, pBuffer + dwBufSize);
        sdk->m_video_buffer.push(data);
    }
    return 0;
}

int DahuaSDK::AudioDataCallback(int lRealHandle, int dwDataType,
                               unsigned char* pBuffer, int dwBufSize, void* dwUser) {
    DahuaSDK* sdk = static_cast<DahuaSDK*>(dwUser);
    if (sdk && pBuffer && dwBufSize > 0) {
        std::lock_guard<std::mutex> lock(sdk->m_buffer_mutex);
        std::vector<uint8_t> data(pBuffer, pBuffer + dwBufSize);
        sdk->m_audio_buffer.push(data);
    }
    return 0;
}

// 宇视SDK实现
UnivviewSDK::UnivviewSDK(const SecurityChipConfig& config)
    : SecurityChipSDK(config)
    , m_session_handle(nullptr)
    , m_stream_handle(nullptr) {
}

UnivviewSDK::~UnivviewSDK() {
    Disconnect();
}

bool UnivviewSDK::ConnectDevice() {
    // 宇视SDK连接实现
    m_session_handle = (void*)0x55555555;
    return true;
}

bool UnivviewSDK::DisconnectDevice() {
    if (m_session_handle) {
        m_session_handle = nullptr;
    }
    return true;
}

bool UnivviewSDK::StartVideoStream() {
    if (!m_session_handle) {
        return false;
    }

    m_stream_handle = (void*)0x66666666;
    return true;
}

bool UnivviewSDK::StartAudioStream() {
    return true; // 宇视音频与视频一起传输
}

bool UnivviewSDK::StopVideoStream() {
    if (m_stream_handle) {
        m_stream_handle = nullptr;
    }
    return true;
}

bool UnivviewSDK::StopAudioStream() {
    return true;
}

void UnivviewSDK::StreamDataCallback(void* handle, void* pUser, void* pData) {
    UnivviewSDK* sdk = static_cast<UnivviewSDK*>(pUser);
    if (sdk && pData) {
        // 处理宇视的流数据格式
        // 这里需要根据宇视SDK的具体数据格式进行解析
    }
}

// SDK工厂类实现
std::unique_ptr<SecurityChipSDK> SecurityChipSDKFactory::CreateSDK(SDKType type, const SecurityChipConfig& config) {
    switch (type) {
        case HIKVISION:
            return std::make_unique<HikvisionSDK>(config);
        case DAHUA:
            return std::make_unique<DahuaSDK>(config);
        case UNIVIEW:
            return std::make_unique<UnivviewSDK>(config);
        default:
            return nullptr;
    }
}

SecurityChipSDKFactory::SDKType SecurityChipSDKFactory::DetectSDKType(const std::string& device_info) {
    if (device_info.find("Hikvision") != std::string::npos ||
        device_info.find("HIKVISION") != std::string::npos) {
        return HIKVISION;
    } else if (device_info.find("Dahua") != std::string::npos ||
               device_info.find("DAHUA") != std::string::npos) {
        return DAHUA;
    } else if (device_info.find("Uniview") != std::string::npos ||
               device_info.find("UNIVIEW") != std::string::npos) {
        return UNIVIEW;
    }

    return GENERIC;
}
