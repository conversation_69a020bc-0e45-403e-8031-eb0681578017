﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="source\rtmp-netconnection.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-netstream.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-control-message.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-handshake.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-chunk-write.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-chunk-read.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-invoke-handler.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-server.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-control-handler.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-event.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-handler.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-client.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\rtmp-chunk-header.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\rtmp-client.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-netstream.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-netconnection.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-control-message.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-msgtypeid.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-handshake.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-event.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-server.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\rtmp-client-invoke-handler.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-chunk-header.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\rtmp-url.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>