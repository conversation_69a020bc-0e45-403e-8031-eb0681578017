﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{B6A95553-A736-4083-8B96-C5F845FA8C6D}</ProjectGuid>
    <RootNamespace>librtp</RootNamespace>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>MultiByte</CharacterSet>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>StaticLibrary</ConfigurationType>
    <PlatformToolset>v143</PlatformToolset>
    <CharacterSet>Unicode</CharacterSet>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>14.0.23107.0</_ProjectFileVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <OutDir>$(SolutionDir)$(Configuration)\</OutDir>
    <IntDir>$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;_DEBUG;_WINDOWS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <MinimalRebuild>true</MinimalRebuild>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader />
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>EditAndContinue</DebugInformationFormat>
      <EnablePREfast>true</EnablePREfast>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <Optimization>Disabled</Optimization>
      <AdditionalIncludeDirectories>.;include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>_DEBUG;_WINDOWS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnablePREfast>true</EnablePREfast>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>.;include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>WIN32;NDEBUG;_WINDOWS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader />
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnablePREfast>true</EnablePREfast>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <Optimization>MaxSpeed</Optimization>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <AdditionalIncludeDirectories>.;include</AdditionalIncludeDirectories>
      <PreprocessorDefinitions>NDEBUG;_WINDOWS;OS_WINDOWS;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <PrecompiledHeader>
      </PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <EnablePREfast>true</EnablePREfast>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClCompile Include="payload\rtp-av1-pack.c" />
    <ClCompile Include="payload\rtp-av1-unpack.c" />
    <ClCompile Include="payload\rtp-h264-bitstream.c" />
    <ClCompile Include="payload\rtp-h265-pack.c" />
    <ClCompile Include="payload\rtp-h265-unpack.c" />
    <ClCompile Include="payload\rtp-h264-pack.c" />
    <ClCompile Include="payload\rtp-h264-unpack.c" />
    <ClCompile Include="payload\rtp-h266-pack.c" />
    <ClCompile Include="payload\rtp-h266-unpack.c" />
    <ClCompile Include="payload\rtp-mp4a-latm-pack.c" />
    <ClCompile Include="payload\rtp-mp4a-latm-unpack.c" />
    <ClCompile Include="payload\rtp-mp4v-es-pack.c" />
    <ClCompile Include="payload\rtp-mp4v-es-unpack.c" />
    <ClCompile Include="payload\rtp-mpeg1or2es-pack.c" />
    <ClCompile Include="payload\rtp-mpeg1or2es-unpack.c" />
    <ClCompile Include="payload\rtp-mpeg4-generic-pack.c" />
    <ClCompile Include="payload\rtp-mpeg4-generic-unpack.c" />
    <ClCompile Include="payload\rtp-pack.c" />
    <ClCompile Include="payload\rtp-payload-helper.c" />
    <ClCompile Include="payload\rtp-payload.c" />
    <ClCompile Include="payload\rtp-ps-unpack.c" />
    <ClCompile Include="payload\rtp-ts-pack.c" />
    <ClCompile Include="payload\rtp-ts-unpack.c" />
    <ClCompile Include="payload\rtp-unpack.c" />
    <ClCompile Include="payload\rtp-vp8-pack.c" />
    <ClCompile Include="payload\rtp-vp8-unpack.c" />
    <ClCompile Include="payload\rtp-vp9-pack.c" />
    <ClCompile Include="payload\rtp-vp9-unpack.c" />
    <ClCompile Include="rtpext\rtp-ext-abs-send-time.c" />
    <ClCompile Include="rtpext\rtp-ext-absolute-capture-time.c" />
    <ClCompile Include="rtpext\rtp-ext-color-space.c" />
    <ClCompile Include="rtpext\rtp-ext-csrc-audio-level.c" />
    <ClCompile Include="rtpext\rtp-ext-frame-marking.c" />
    <ClCompile Include="rtpext\rtp-ext-inband-cn.c" />
    <ClCompile Include="rtpext\rtp-ext-playout-delay.c" />
    <ClCompile Include="rtpext\rtp-ext-sdes.c" />
    <ClCompile Include="rtpext\rtp-ext-ssrc-audio-level.c" />
    <ClCompile Include="rtpext\rtp-ext-toffset.c" />
    <ClCompile Include="rtpext\rtp-ext-transport-wide-cc.c" />
    <ClCompile Include="rtpext\rtp-ext-video-content-type.c" />
    <ClCompile Include="rtpext\rtp-ext-video-frame-tracking-id.c" />
    <ClCompile Include="rtpext\rtp-ext-video-layers-allocation.c" />
    <ClCompile Include="rtpext\rtp-ext-video-orientation.c" />
    <ClCompile Include="rtpext\rtp-ext-video-timing.c" />
    <ClCompile Include="rtpext\rtp-ext.c" />
    <ClCompile Include="source\rtcp-app.c" />
    <ClCompile Include="source\rtcp-bye.c" />
    <ClCompile Include="source\rtcp-interval.c" />
    <ClCompile Include="source\rtcp-psfb.c" />
    <ClCompile Include="source\rtcp-rr.c" />
    <ClCompile Include="source\rtcp-rtpfb.c" />
    <ClCompile Include="source\rtcp-sdec.c" />
    <ClCompile Include="source\rtcp-sr.c" />
    <ClCompile Include="source\rtcp-xr.c" />
    <ClCompile Include="source\rtcp.c" />
    <ClCompile Include="source\rtp-demuxer.c" />
    <ClCompile Include="source\rtp-member-list.c" />
    <ClCompile Include="source\rtp-member.c" />
    <ClCompile Include="source\rtp-packet.c" />
    <ClCompile Include="source\rtp-profile.c" />
    <ClCompile Include="source\rtp-queue.c" />
    <ClCompile Include="source\rtp-ssrc.c" />
    <ClCompile Include="source\rtp-time.c" />
    <ClCompile Include="source\rtp.c" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\rtcp-header.h" />
    <ClInclude Include="include\rtp-demuxer.h" />
    <ClInclude Include="include\rtp-ext.h" />
    <ClInclude Include="include\rtp-header-extension.h" />
    <ClInclude Include="include\rtp-header.h" />
    <ClInclude Include="include\rtp-internal.h" />
    <ClInclude Include="include\rtp-member-list.h" />
    <ClInclude Include="include\rtp-member.h" />
    <ClInclude Include="include\rtp-packet.h" />
    <ClInclude Include="include\rtp-param.h" />
    <ClInclude Include="include\rtp-payload.h" />
    <ClInclude Include="include\rtp-profile.h" />
    <ClInclude Include="include\rtp-queue.h" />
    <ClInclude Include="include\rtp-util.h" />
    <ClInclude Include="include\rtp.h" />
    <ClInclude Include="payload\rtp-payload-helper.h" />
    <ClInclude Include="payload\rtp-payload-internal.h" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>