#include "MediaSourceManager.h"
#include "LiveMediaSource.h"
#include "FileMediaSource.h"
#include <iostream>

MediaSourceManager::MediaSourceManager() {
}

MediaSourceManager::~MediaSourceManager() {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_sources.clear();
}

std::shared_ptr<MediaSource> MediaSourceManager::CreateOrGetSource(const std::string& path, bool isLive) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_sources.find(path);
    if (it != m_sources.end()) {
        return it->second;
    }
    
    std::shared_ptr<MediaSource> source;
    if (isLive) {
        source = std::make_shared<LiveMediaSource>(path);
    } else {
        // 文件源需要额外的文件路径参数
        return nullptr;
    }
    
    m_sources[path] = source;
    return source;
}

void MediaSourceManager::RemoveSource(const std::string& path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    m_sources.erase(path);
}

std::shared_ptr<MediaSource> MediaSourceManager::FindSource(const std::string& path) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_sources.find(path);
    if (it != m_sources.end()) {
        return it->second;
    }
    
    return nullptr;
}

void MediaSourceManager::ProcessMediaData() {
    // 这里可以添加定期清理无效源的逻辑
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_sources.begin();
    while (it != m_sources.end()) {
        // 检查源是否还有效
        if (it->second.use_count() <= 1) {
            // 只有管理器持有引用，可以删除
            it = m_sources.erase(it);
        } else {
            ++it;
        }
    }
}

std::shared_ptr<MediaSource> MediaSourceManager::CreateFileSource(const std::string& path, const std::string& filePath) {
    std::lock_guard<std::mutex> lock(m_mutex);
    
    auto it = m_sources.find(path);
    if (it != m_sources.end()) {
        return it->second;
    }
    
    auto source = std::make_shared<FileMediaSource>(path, filePath);
    m_sources[path] = source;
    return source;
}