#include "sockutil.h"
#include <string.h>
#include <stdio.h>

#ifdef _WIN32
    #pragma comment(lib, "ws2_32.lib")
#else
    #include <netdb.h>
    #include <poll.h>
#endif

int socket_init(void) {
#ifdef _WIN32
    WSADATA wsaData;
    return WSAStartup(MAKEWORD(2, 2), &wsaData);
#else
    return 0;
#endif
}

int socket_cleanup(void) {
#ifdef _WIN32
    return WSACleanup();
#else
    return 0;
#endif
}

socket_t socket_tcp(void) {
    return socket(AF_INET, SOCK_STREAM, IPPROTO_TCP);
}

socket_t socket_udp(void) {
    return socket(AF_INET, SOCK_DGRAM, IPPROTO_UDP);
}

int socket_close(socket_t sock) {
    if (sock == socket_invalid)
        return -1;
        
#ifdef _WIN32
    return closesocket(sock);
#else
    return close(sock);
#endif
}

socket_t socket_tcp_listen_ipv4(const char* ip, uint16_t port, int backlog) {
    socket_t sock = socket_tcp();
    if (sock == socket_invalid)
        return socket_invalid;
        
    // 设置地址重用
    int reuse = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, (const char*)&reuse, sizeof(reuse));
    
    // 绑定地址
    struct sockaddr_in addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin_family = AF_INET;
    addr.sin_port = htons(port);
    
    if (ip) {
        inet_pton(AF_INET, ip, &addr.sin_addr);
    } else {
        addr.sin_addr.s_addr = htonl(INADDR_ANY);
    }
    
    if (bind(sock, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        socket_close(sock);
        return socket_invalid;
    }
    
    // 监听
    if (listen(sock, backlog) < 0) {
        socket_close(sock);
        return socket_invalid;
    }
    
    return sock;
}

socket_t socket_tcp_listen_ipv6(const char* ip, uint16_t port, int backlog) {
    socket_t sock = socket(AF_INET6, SOCK_STREAM, IPPROTO_TCP);
    if (sock == socket_invalid)
        return socket_invalid;
        
    // 设置地址重用
    int reuse = 1;
    setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, (const char*)&reuse, sizeof(reuse));
    
    // 绑定地址
    struct sockaddr_in6 addr;
    memset(&addr, 0, sizeof(addr));
    addr.sin6_family = AF_INET6;
    addr.sin6_port = htons(port);
    
    if (ip) {
        inet_pton(AF_INET6, ip, &addr.sin6_addr);
    } else {
        addr.sin6_addr = in6addr_any;
    }
    
    if (bind(sock, (struct sockaddr*)&addr, sizeof(addr)) < 0) {
        socket_close(sock);
        return socket_invalid;
    }
    
    // 监听
    if (listen(sock, backlog) < 0) {
        socket_close(sock);
        return socket_invalid;
    }
    
    return sock;
}

socket_t socket_accept(socket_t sock, struct sockaddr_storage* addr, socklen_t* addrlen) {
    return accept(sock, (struct sockaddr*)addr, addrlen);
}

socket_t socket_connect_host(const char* host, uint16_t port, int timeout) {
    struct addrinfo hints, *result;
    char service[16];
    
    memset(&hints, 0, sizeof(hints));
    hints.ai_family = AF_UNSPEC;
    hints.ai_socktype = SOCK_STREAM;
    
    snprintf(service, sizeof(service), "%u", port);
    
    if (getaddrinfo(host, service, &hints, &result) != 0) {
        return socket_invalid;
    }
    
    socket_t sock = socket_invalid;
    for (struct addrinfo* rp = result; rp != NULL; rp = rp->ai_next) {
        sock = socket(rp->ai_family, rp->ai_socktype, rp->ai_protocol);
        if (sock == socket_invalid)
            continue;
            
        // 设置非阻塞模式进行连接
        socket_setnonblock(sock, 1);
        
        if (connect(sock, rp->ai_addr, rp->ai_addrlen) == 0) {
            // 连接成功
            socket_setnonblock(sock, 0);
            break;
        }
        
#ifdef _WIN32
        if (WSAGetLastError() == WSAEWOULDBLOCK) {
#else
        if (errno == EINPROGRESS) {
#endif
            // 等待连接完成
            fd_set wfds;
            struct timeval tv;
            
            FD_ZERO(&wfds);
            FD_SET(sock, &wfds);
            
            tv.tv_sec = timeout / 1000;
            tv.tv_usec = (timeout % 1000) * 1000;
            
            if (select(sock + 1, NULL, &wfds, NULL, &tv) > 0) {
                int error = 0;
                socklen_t len = sizeof(error);
                getsockopt(sock, SOL_SOCKET, SO_ERROR, (char*)&error, &len);
                
                if (error == 0) {
                    // 连接成功
                    socket_setnonblock(sock, 0);
                    break;
                }
            }
        }
        
        socket_close(sock);
        sock = socket_invalid;
    }
    
    freeaddrinfo(result);
    return sock;
}

int socket_send(socket_t sock, const void* buf, size_t len, int flags) {
    return send(sock, (const char*)buf, len, flags);
}

int socket_recv(socket_t sock, void* buf, size_t len, int flags) {
    return recv(sock, (char*)buf, len, flags);
}

int socket_send_all_by_time(socket_t sock, const void* buf, size_t len, int flags, int timeout) {
    const char* p = (const char*)buf;
    size_t remain = len;
    
    while (remain > 0) {
        int r = send(sock, p, remain, flags);
        if (r > 0) {
            p += r;
            remain -= r;
        } else if (r == 0) {
            return len - remain;
        } else {
#ifdef _WIN32
            if (WSAGetLastError() == WSAEWOULDBLOCK) {
#else
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
#endif
                // 等待可写
                fd_set wfds;
                struct timeval tv;
                
                FD_ZERO(&wfds);
                FD_SET(sock, &wfds);
                
                tv.tv_sec = timeout / 1000;
                tv.tv_usec = (timeout % 1000) * 1000;
                
                if (select(sock + 1, NULL, &wfds, NULL, &tv) <= 0) {
                    return -1;
                }
            } else {
                return -1;
            }
        }
    }
    
    return len;
}

void socket_setbufvec(socket_bufvec_t* vec, int idx, void* base, size_t len) {
    vec[idx].iov_base = base;
    vec[idx].iov_len = len;
}

int socket_send_v_all_by_time(socket_t sock, const socket_bufvec_t* vec, int n, int flags, int timeout) {
    // 简单实现：逐个发送
    size_t total = 0;
    
    for (int i = 0; i < n; i++) {
        int r = socket_send_all_by_time(sock, vec[i].iov_base, vec[i].iov_len, flags, timeout);
        if (r < 0) {
            return -1;
        }
        total += r;
        if (r < vec[i].iov_len) {
            break;
        }
    }
    
    return total;
}

int socket_setnonblock(socket_t sock, int nonblock) {
#ifdef _WIN32
    u_long mode = nonblock ? 1 : 0;
    return ioctlsocket(sock, FIONBIO, &mode);
#else
    int flags = fcntl(sock, F_GETFL, 0);
    if (flags < 0)
        return -1;
        
    if (nonblock) {
        flags |= O_NONBLOCK;
    } else {
        flags &= ~O_NONBLOCK;
    }
    
    return fcntl(sock, F_SETFL, flags);
#endif
}

int socket_setkeepalive(socket_t sock, int enable) {
    return setsockopt(sock, SOL_SOCKET, SO_KEEPALIVE, (const char*)&enable, sizeof(enable));
}

int socket_setreuse(socket_t sock, int enable) {
    return setsockopt(sock, SOL_SOCKET, SO_REUSEADDR, (const char*)&enable, sizeof(enable));
}

int socket_addr_from(struct sockaddr_storage* ss, socklen_t* len, const char* ip, uint16_t port) {
    memset(ss, 0, sizeof(*ss));
    
    // 尝试IPv4
    struct sockaddr_in* addr4 = (struct sockaddr_in*)ss;
    if (inet_pton(AF_INET, ip, &addr4->sin_addr) == 1) {
        addr4->sin_family = AF_INET;
        addr4->sin_port = htons(port);
        *len = sizeof(struct sockaddr_in);
        return 0;
    }
    
    // 尝试IPv6
    struct sockaddr_in6* addr6 = (struct sockaddr_in6*)ss;
    if (inet_pton(AF_INET6, ip, &addr6->sin6_addr) == 1) {
        addr6->sin6_family = AF_INET6;
        addr6->sin6_port = htons(port);
        *len = sizeof(struct sockaddr_in6);
        return 0;
    }
    
    return -1;
}

int socket_addr_to(const struct sockaddr_storage* ss, socklen_t len, char* ip, uint16_t* port) {
    if (ss->ss_family == AF_INET) {
        const struct sockaddr_in* addr4 = (const struct sockaddr_in*)ss;
        if (inet_ntop(AF_INET, &addr4->sin_addr, ip, INET_ADDRSTRLEN) == NULL)
            return -1;
        *port = ntohs(addr4->sin_port);
        return 0;
    } else if (ss->ss_family == AF_INET6) {
        const struct sockaddr_in6* addr6 = (const struct sockaddr_in6*)ss;
        if (inet_ntop(AF_INET6, &addr6->sin6_addr, ip, INET6_ADDRSTRLEN) == NULL)
            return -1;
        *port = ntohs(addr6->sin6_port);
        return 0;
    }
    
    return -1;
}

int socket_getname(socket_t sock, char* ip, uint16_t* port) {
    struct sockaddr_storage addr;
    socklen_t addrlen = sizeof(addr);
    
    if (getsockname(sock, (struct sockaddr*)&addr, &addrlen) < 0)
        return -1;
        
    return socket_addr_to(&addr, addrlen, ip, port);
}

int socket_getpeername(socket_t sock, char* ip, uint16_t* port) {
    struct sockaddr_storage addr;
    socklen_t addrlen = sizeof(addr);
    
    if (getpeername(sock, (struct sockaddr*)&addr, &addrlen) < 0)
        return -1;
        
    return socket_addr_to(&addr, addrlen, ip, port);
}