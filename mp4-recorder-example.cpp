#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include "recording-manager.h"

static bool g_running = true;

void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        g_running = false;
        std::cout << "\nReceived signal, stopping..." << std::endl;
    }
}

// 示例：创建录像任务
RecordingTask CreateSampleTask(const std::string& device_id, const std::string& ip_address) {
    RecordingTask task;
    
    // 基本信息
    task.task_id = "task_" + device_id;
    task.device_id = device_id;
    task.sdk_type = SecurityChipSDKFactory::HIKVISION; // 假设使用海康威视设备
    
    // 设备连接配置
    task.chip_config.device_id = device_id;
    task.chip_config.ip_address = ip_address;
    task.chip_config.port = 8000;
    task.chip_config.username = "admin";
    task.chip_config.password = "123456";
    task.chip_config.video_channel = 0;
    task.chip_config.video_stream = 0; // 主码流
    task.chip_config.enable_audio = true;
    
    // 视频参数
    task.chip_config.video_width = 1920;
    task.chip_config.video_height = 1080;
    task.chip_config.video_fps = 25;
    task.chip_config.video_bitrate = 2048;
    
    // 音频参数
    task.chip_config.audio_sample_rate = 48000;
    task.chip_config.audio_channels = 2;
    task.chip_config.audio_bits_per_sample = 16;
    
    // 录像配置
    task.recording_config.output_dir = "./recordings/" + device_id;
    task.recording_config.max_file_size = 100 * 1024 * 1024; // 100MB
    task.recording_config.max_duration = 300; // 5分钟
    task.recording_config.enable_hash_tagging = true;
    task.recording_config.file_prefix = device_id + "_record";
    
    // 录像策略
    task.continuous_recording = true;
    task.motion_detection = false;
    task.alarm_trigger = false;
    task.pre_record_seconds = 5;
    task.post_record_seconds = 10;
    
    // 添加标签
    task.tags["location"] = "Building_A_Floor_1";
    task.tags["camera_type"] = "Dome";
    task.tags["resolution"] = "1080P";
    task.tags["department"] = "Security";
    
    return task;
}

// 事件回调函数
void OnTaskStateChange(const std::string& task_id, TaskState old_state, TaskState new_state) {
    std::cout << "Task " << task_id << " state changed from " 
              << static_cast<int>(old_state) << " to " << static_cast<int>(new_state) << std::endl;
}

void OnRecordingComplete(const std::string& task_id, const RecordingInfo& info) {
    std::cout << "Recording completed for task " << task_id << ":" << std::endl;
    std::cout << "  File: " << info.filename << std::endl;
    std::cout << "  Size: " << info.file_size << " bytes" << std::endl;
    std::cout << "  Duration: " << info.duration << " seconds" << std::endl;
    std::cout << "  MD5: " << info.hash_md5 << std::endl;
    std::cout << "  SHA256: " << info.hash_sha256 << std::endl;
}

void OnError(const std::string& task_id, const std::string& error) {
    std::cerr << "Error in task " << task_id << ": " << error << std::endl;
}

void OnStorageWarning(uint64_t used_bytes, uint64_t total_bytes) {
    double usage_percent = (double)used_bytes / total_bytes * 100.0;
    std::cout << "Storage warning: " << usage_percent << "% used (" 
              << used_bytes / (1024*1024) << "MB / " 
              << total_bytes / (1024*1024) << "MB)" << std::endl;
}

// 打印统计信息
void PrintStatistics(const RecordingManager& manager) {
    auto stats = manager.GetStatistics();
    
    std::cout << "\n=== Recording Statistics ===" << std::endl;
    std::cout << "Total Tasks: " << stats.total_tasks << std::endl;
    std::cout << "Running Tasks: " << stats.running_tasks << std::endl;
    std::cout << "Total Recordings: " << stats.total_recordings << std::endl;
    std::cout << "Total Storage Used: " << stats.total_storage_used / (1024*1024) << " MB" << std::endl;
    std::cout << "Recordings Today: " << stats.recordings_today << std::endl;
    std::cout << "Recordings This Week: " << stats.recordings_this_week << std::endl;
    std::cout << "Recordings This Month: " << stats.recordings_this_month << std::endl;
    std::cout << "===========================\n" << std::endl;
}

// 演示录像文件搜索功能
void DemoSearchFunctionality(const RecordingManager& manager) {
    std::cout << "\n=== Search Demo ===" << std::endl;
    
    // 搜索特定设备的录像
    auto device_recordings = manager.GetRecordingsByDevice("camera_001");
    std::cout << "Found " << device_recordings.size() << " recordings for device camera_001" << std::endl;
    
    // 按时间范围搜索
    auto now = std::chrono::system_clock::now();
    auto one_hour_ago = now - std::chrono::hours(1);
    auto time_range_recordings = manager.GetRecordingsByTimeRange(one_hour_ago, now);
    std::cout << "Found " << time_range_recordings.size() << " recordings in the last hour" << std::endl;
    
    // 按关键词搜索
    auto search_results = manager.SearchRecordings("Building_A");
    std::cout << "Found " << search_results.size() << " recordings matching 'Building_A'" << std::endl;
    
    // 显示搜索结果详情
    for (const auto& recording : search_results) {
        std::cout << "  - " << recording.filename 
                  << " (" << recording.file_size / (1024*1024) << "MB, "
                  << recording.duration << "s)" << std::endl;
    }
    
    std::cout << "==================\n" << std::endl;
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    std::cout << "MP4 Recorder Example Starting..." << std::endl;
    
    // 创建录像管理器
    RecordingManager manager;
    
    // 设置事件回调
    manager.SetTaskStateChangeCallback(OnTaskStateChange);
    manager.SetRecordingCompleteCallback(OnRecordingComplete);
    manager.SetErrorCallback(OnError);
    manager.SetStorageWarningCallback(OnStorageWarning);
    
    // 启动管理器
    if (!manager.Start()) {
        std::cerr << "Failed to start recording manager" << std::endl;
        return -1;
    }
    
    // 创建示例录像任务
    std::vector<RecordingTask> tasks = {
        CreateSampleTask("camera_001", "192.168.1.100"),
        CreateSampleTask("camera_002", "192.168.1.101"),
        CreateSampleTask("camera_003", "192.168.1.102")
    };
    
    // 添加任务到管理器
    for (const auto& task : tasks) {
        if (manager.AddTask(task)) {
            std::cout << "Added task: " << task.task_id << std::endl;
        } else {
            std::cerr << "Failed to add task: " << task.task_id << std::endl;
        }
    }
    
    // 启动所有任务
    if (manager.StartAllTasks()) {
        std::cout << "All tasks started successfully" << std::endl;
    } else {
        std::cerr << "Failed to start some tasks" << std::endl;
    }
    
    // 主循环
    auto last_stats_time = std::chrono::steady_clock::now();
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 每30秒打印一次统计信息
        auto now = std::chrono::steady_clock::now();
        if (now - last_stats_time >= std::chrono::seconds(30)) {
            PrintStatistics(manager);
            DemoSearchFunctionality(manager);
            last_stats_time = now;
        }
    }
    
    // 停止所有任务
    std::cout << "Stopping all tasks..." << std::endl;
    manager.StopAllTasks();
    
    // 停止管理器
    manager.Stop();
    
    // 最终统计
    PrintStatistics(manager);
    
    std::cout << "MP4 Recorder Example Stopped." << std::endl;
    return 0;
}
