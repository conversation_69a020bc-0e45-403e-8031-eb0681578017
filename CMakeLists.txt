cmake_minimum_required(VERSION 3.10)
project(MP4Recorder)

# C++标准
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -O2")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")

# 查找必要的库
find_package(Threads REQUIRED)
find_package(PkgConfig REQUIRED)

# 查找OpenSSL
find_package(OpenSSL REQUIRED)

# 查找jsoncpp
pkg_check_modules(J<PERSON><PERSON><PERSON> jsoncpp)
if(NOT JSONCPP_FOUND)
    find_package(PkgConfig REQUIRED)
    pkg_check_modules(JSONCPP REQUIRED jsoncpp)
endif()

# 设置media-server和sdk路径
set(MEDIA_SERVER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/media-server)
set(SDK_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../sdk)

# 检查media-server目录是否存在
if(NOT EXISTS ${MEDIA_SERVER_DIR})
    message(WARNING "media-server directory not found at ${MEDIA_SERVER_DIR}")
    message(WARNING "Please clone media-server repository to the project directory")
endif()

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${MEDIA_SERVER_DIR}/libmov/include
    ${MEDIA_SERVER_DIR}/libflv/include
    ${MEDIA_SERVER_DIR}/librtmp/include
    ${MEDIA_SERVER_DIR}/librtsp/include
    ${MEDIA_SERVER_DIR}/libhls/include
    ${MEDIA_SERVER_DIR}/librtp/include
    ${MEDIA_SERVER_DIR}/libmpeg/include
    ${SDK_DIR}/include
    ${SDK_DIR}/libaio/include
    ${SDK_DIR}/libhttp/include
    ${OPENSSL_INCLUDE_DIR}
    ${JSONCPP_INCLUDE_DIRS}
)

# 库目录
link_directories(
    ${MEDIA_SERVER_DIR}/libmov
    ${MEDIA_SERVER_DIR}/libflv
    ${MEDIA_SERVER_DIR}/librtmp
    ${MEDIA_SERVER_DIR}/librtsp
    ${MEDIA_SERVER_DIR}/libhls
    ${MEDIA_SERVER_DIR}/librtp
    ${MEDIA_SERVER_DIR}/libmpeg
    ${SDK_DIR}/libaio
    ${SDK_DIR}/libhttp
)

# MP4录像器库源文件
set(MP4_RECORDER_SOURCES
    mp4-recorder.cpp
    security-chip-sdk.cpp
    recording-manager.cpp
)

# MP4录像器库头文件
set(MP4_RECORDER_HEADERS
    mp4-recorder.h
    security-chip-sdk.h
    recording-manager.h
)

# 创建MP4录像器静态库
add_library(mp4recorder STATIC ${MP4_RECORDER_SOURCES})

# 链接库到MP4录像器库
target_link_libraries(mp4recorder
    mov
    flv
    rtmp
    rtsp
    hls
    rtp
    mpeg
    ${OPENSSL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    Threads::Threads
)

# 示例程序
add_executable(mp4-recorder-example mp4-recorder-example.cpp)
target_link_libraries(mp4-recorder-example
    mp4recorder
    mov
    flv
    rtmp
    rtsp
    hls
    rtp
    mpeg
    ${OPENSSL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    Threads::Threads
)

# 测试程序
add_executable(mp4-recorder-test mp4-recorder-test.cpp)
target_link_libraries(mp4-recorder-test
    mp4recorder
    mov
    flv
    rtmp
    rtsp
    hls
    rtp
    mpeg
    ${OPENSSL_LIBRARIES}
    ${JSONCPP_LIBRARIES}
    Threads::Threads
)

# 安装
install(TARGETS mp4recorder DESTINATION lib)
install(FILES ${MP4_RECORDER_HEADERS} DESTINATION include)
install(TARGETS mp4-recorder-example DESTINATION bin)
install(TARGETS mp4-recorder-test DESTINATION bin)

# 创建必要的目录
install(DIRECTORY DESTINATION ${CMAKE_INSTALL_PREFIX}/var/recordings)
install(DIRECTORY DESTINATION ${CMAKE_INSTALL_PREFIX}/etc/mp4-recorder)

# 配置文件
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/config/recording_config.json.example
    ${CMAKE_CURRENT_BINARY_DIR}/recording_config.json
    COPYONLY
)

install(FILES ${CMAKE_CURRENT_BINARY_DIR}/recording_config.json 
        DESTINATION ${CMAKE_INSTALL_PREFIX}/etc/mp4-recorder)

# 打包配置
set(CPACK_PACKAGE_NAME "MP4Recorder")
set(CPACK_PACKAGE_VERSION "1.0.0")
set(CPACK_PACKAGE_DESCRIPTION_SUMMARY "MP4 Recorder for Security Cameras")
set(CPACK_PACKAGE_VENDOR "Security Solutions")
set(CPACK_GENERATOR "TGZ;DEB;RPM")

include(CPack)

# 显示配置信息
message(STATUS "MP4 Recorder Configuration:")
message(STATUS "  Build type: ${CMAKE_BUILD_TYPE}")
message(STATUS "  C++ standard: ${CMAKE_CXX_STANDARD}")
message(STATUS "  Media Server Dir: ${MEDIA_SERVER_DIR}")
message(STATUS "  SDK Dir: ${SDK_DIR}")
message(STATUS "  OpenSSL found: ${OPENSSL_FOUND}")
message(STATUS "  JsonCpp found: ${JSONCPP_FOUND}")
message(STATUS "  Install prefix: ${CMAKE_INSTALL_PREFIX}")
