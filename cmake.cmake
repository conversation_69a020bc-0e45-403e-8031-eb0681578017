cmake_minimum_required(VERSION 3.10)
project(MediaServer)

# C++标准
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 编译选项
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -O2")
set(CMAKE_CXX_FLAGS_DEBUG "${CMAKE_CXX_FLAGS_DEBUG} -g")

# 查找线程库
find_package(Threads REQUIRED)

# 设置media-server和sdk路径
set(MEDIA_SERVER_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../media-server)
set(SDK_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../sdk)

# 包含目录
include_directories(
    ${CMAKE_CURRENT_SOURCE_DIR}
    ${MEDIA_SERVER_DIR}/librtmp/include
    ${MEDIA_SERVER_DIR}/librtsp/include
    ${MEDIA_SERVER_DIR}/libhls/include
    ${MEDIA_SERVER_DIR}/libflv/include
    ${MEDIA_SERVER_DIR}/librtp/include
    ${MEDIA_SERVER_DIR}/libmpeg/include
    ${SDK_DIR}/include
    ${SDK_DIR}/libaio/include
    ${SDK_DIR}/libhttp/include
)

# 库目录
link_directories(
    ${MEDIA_SERVER_DIR}/librtmp
    ${MEDIA_SERVER_DIR}/librtsp
    ${MEDIA_SERVER_DIR}/libhls
    ${MEDIA_SERVER_DIR}/libflv
    ${MEDIA_SERVER_DIR}/librtp
    ${MEDIA_SERVER_DIR}/libmpeg
    ${SDK_DIR}/libaio
    ${SDK_DIR}/libhttp
)

# 源文件
set(SOURCES
    main.cpp
    MediaServer.cpp
    MediaSourceManager.cpp
    RtmpServer.cpp
    RtspServer.cpp
    HlsServer.cpp
    HttpFlvServer.cpp
    LiveMediaSource.cpp
    FileMediaSource.cpp
)

# 可执行文件
add_executable(mediaserver ${SOURCES})

# 链接库
target_link_libraries(mediaserver
    rtmp
    rtsp
    hls
    flv
    rtp
    mpeg
    aio
    http
    Threads::Threads
    rt
)

# 安装
install(TARGETS mediaserver DESTINATION bin)

# 创建媒体目录
install(DIRECTORY DESTINATION ${CMAKE_INSTALL_PREFIX}/var/media)

# 配置文件示例
configure_file(
    ${CMAKE_CURRENT_SOURCE_DIR}/mediaserver.conf.example
    ${CMAKE_CURRENT_BINARY_DIR}/mediaserver.conf
    COPYONLY
)