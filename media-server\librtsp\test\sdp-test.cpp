#include "sdp.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

static char s_sdp[64 * 1024];

static const char* sdp_read(const char* file)
{
	FILE* fp = fopen(file, "rb");
	fread(s_sdp, 1, sizeof(s_sdp), fp);
	fclose(fp);
	return s_sdp;
}

void sdp_test1(const char* file)
{
	const char* txt = sdp_read(file);
	sdp_t* sdp = sdp_parse(txt, strlen(txt));
	sdp_destroy(sdp);
}

static void sdp_test2()
{
	const char* txt = "v=0\n\
o=- 0 0 IN IP4 *************\n\
s=-\n\
c=IN IP4 *************\n\
m=video 10088 RTP/AVP 100\n\
a=rtpmap:100 H264/90000\n\
a=fmtp:100 CIF=1;4CIF=1;F=1;K=1\n\
f=v//5///a///\n\
a=sendrecv";
	sdp_t* sdp = sdp_parse(txt, strlen(txt));
	sdp_destroy(sdp);
}

static void sdp_test3()
{
	const char txt[] = { 0x76, 0x3d, 0x30, 0x0d, 0x0a, 0x6f, 0x3d, 0x2d, 0x20, 0x31, 0x33, 0x37, 0x34, 0x38, 0x31, 0x31, 0x31, 0x20, 0x31, 0x20, 0x49, 0x4e, 0x20, 0x49, 0x50, 0x34, 0x20, 0x31, 0x32, 0x37, 0x2e, 0x30, 0x2e, 0x31, 0x2e, 0x31, 0x0d, 0x0a, 0x73, 0x3d, 0x72, 0x74, 0x73, 0x70, 0x20, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x20, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x0d, 0x0a, 0x69, 0x3d, 0x54, 0x68, 0x75, 0x20, 0x4a, 0x61, 0x6e, 0x20, 0x20, 0x31, 0x20, 0x30, 0x38, 0x3a, 0x30, 0x30, 0x3a, 0x31, 0x33, 0x20, 0x31, 0x39, 0x37, 0x30, 0x0a, 0x0d, 0x0a, 0x74, 0x3d, 0x30, 0x20, 0x30, 0x0d, 0x0a, 0x61, 0x3d, 0x74, 0x6f, 0x6f, 0x6c, 0x3a, 0x4c, 0x49, 0x56, 0x45, 0x35, 0x35, 0x35, 0x20, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x69, 0x6e, 0x67, 0x20, 0x4d, 0x65, 0x64, 0x69, 0x61, 0x20, 0x76 };
	sdp_t* sdp = sdp_parse(txt, strlen(txt));
	sdp_destroy(sdp);
}

static void sdp_test4()
{
	const char* txt = "v=0\n\
o=- 1001 1 IN\n\
s=VCP IPC Realtime stream\n\
m=video 0 RTP/AVP 105\n\
c=IN\n\
a=control:rtsp://************/media/video1/video\n\
a=rtpmap:105 H264/90000\n\
a=fmtp:105 profile-level-id=4d001f; packetization-mode=1; sprop-parameter-sets=Z00AH541wKALdNwEBAUAAAMD6AAAw1CE,aO4xsg==\n\
a=recvonly\n\
m=application 0 RTP/AVP 107\n\
c=IN\n\
a=control:rtsp://************/media/video1/metadata\n\
a=rtpmap:107 vnd.onvif.metadata/90000\n\
a=fmtp:107 DecoderTag=h3c-v3 RTCP=0\n\
a=recvonly";
	sdp_t* sdp = sdp_parse(txt, strlen(txt));
	sdp_destroy(sdp);
}

void sdp_test()
{
	sdp_test2();
	sdp_test3();
	sdp_test4();
}