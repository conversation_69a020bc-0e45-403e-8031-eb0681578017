#ifndef RTSP_SERVER_H_
#define RTSP_SERVER_H_

#include <memory>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>
#include "rtsp-server.h"
#include "sockutil.h"
#include "MediaSourceManager.h"
#include "rtp-profile.h"
#include "rtsp-header-transport.h"

class RtspSession;

class RtspServer {
public:
    RtspServer(std::shared_ptr<MediaSourceManager> sourceManager);
    ~RtspServer();
    
    bool Start(int port);
    void Stop();
    
private:
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::atomic<bool> m_running{false};
    socket_t m_socket;
    std::thread m_acceptThread;
    
    std::mutex m_sessionMutex;
    std::map<void*, std::shared_ptr<RtspSession>> m_sessions;
    
    void AcceptThread();
    void HandleClient(socket_t client, const struct sockaddr_storage& addr);
    
    // RTSP回调
    static int OnDescribe(void* ptr, rtsp_server_t* rtsp, const char* uri);
    static int OnSetup(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                       const struct rtsp_header_transport_t transports[], size_t num);
    static int OnPlay(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                      const int64_t *npt, const double *scale);
    static int OnPause(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                       const int64_t *npt);
    static int OnTeardown(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session);
    static int OnAnnounce(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* sdp, int len);
    static int OnRecord(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                        const int64_t *npt, const double *scale);
    static int OnOptions(void* ptr, rtsp_server_t* rtsp, const char* uri);
    static int OnGetParameter(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                              const void* content, int bytes);
    static int OnSetParameter(void* ptr, rtsp_server_t* rtsp, const char* uri, const char* session, 
                              const void* content, int bytes);
};

// RTSP媒体传输
class RtspMediaTransport {
public:
    enum TransportMode {
        RTP_OVER_UDP,
        RTP_OVER_TCP
    };
    
    RtspMediaTransport();
    virtual ~RtspMediaTransport();
    
    virtual int SendRTP(const void* data, size_t bytes) = 0;
    virtual int SendRTCP(const void* data, size_t bytes) = 0;
};

// RTSP会话
class RtspSession : public MediaSession, public std::enable_shared_from_this<RtspSession> {
public:
    RtspSession(socket_t socket, const struct sockaddr_storage& addr, 
                rtsp_server_t* rtsp, std::shared_ptr<MediaSourceManager> sourceManager);
    ~RtspSession();
    
    void Start();
    void Stop();
    
    // MediaSession接口
    void OnMediaPacket(const MediaPacket& packet) override;
    void OnMediaInfo(const MediaInfo& info) override;
    
    // RTSP操作
    int Describe(const char* uri);
    int Setup(const char* uri, const struct rtsp_header_transport_t transports[], size_t num);
    int Play(const char* uri);
    int Pause(const char* uri);
    int Teardown(const char* uri);
    
    // 网络发送
    int Send(const void* data, size_t bytes);
    
    // 获取RTSP服务器
    rtsp_server_t* GetRtspServer() { return m_rtsp; }
    
    // 处理网络数据
    void ProcessData();

private:
    socket_t m_socket;
    struct sockaddr_storage m_addr;
    rtsp_server_t* m_rtsp;
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::shared_ptr<MediaSource> m_mediaSource;
    std::atomic<bool> m_running{false};
    std::thread m_thread;
    
    // 会话信息
    std::string m_sessionId;
    std::string m_streamPath;
    
    // 媒体信息
    MediaInfo m_mediaInfo;
    std::string m_sdp;
    
    // RTP传输
    struct MediaTrack {
        int trackId;
        int payloadType;
        std::unique_ptr<RtspMediaTransport> transport;
        uint32_t ssrc;
        uint16_t sequence;
        uint32_t timestamp;
    };
    
    std::vector<MediaTrack> m_tracks;
    
    void NetworkThread();
    void GenerateSDP();
    void SendRTPPacket(int trackId, const MediaPacket& packet);
};

#endif // RTSP_SERVER_H_