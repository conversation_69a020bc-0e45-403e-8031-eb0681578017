#include "recording-manager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <algorithm>
#include <json/json.h>

RecordingManager::RecordingManager()
    : m_recordings_db_file("./recordings/recordings_db.json")
    , m_config_file("./config/recording_config.json")
    , m_storage_warning_threshold(80 * 1024 * 1024 * 1024ULL) // 80GB
    , m_cleanup_interval_hours(24) {
    
    // 创建必要的目录
    std::filesystem::create_directories("./recordings");
    std::filesystem::create_directories("./config");
    
    // 加载所有录像记录
    LoadAllRecordings();
}

RecordingManager::~RecordingManager() {
    Stop();
}

bool RecordingManager::Start() {
    if (m_running) {
        return true;
    }
    
    m_running = true;
    m_manager_thread = std::thread(&RecordingManager::ManagerThread, this);
    
    return true;
}

bool RecordingManager::Stop() {
    if (!m_running) {
        return true;
    }
    
    // 停止所有任务
    StopAllTasks();
    
    // 停止管理线程
    m_running = false;
    if (m_manager_thread.joinable()) {
        m_manager_thread.join();
    }
    
    // 保存录像数据库
    SaveAllRecordings();
    
    return true;
}

bool RecordingManager::AddTask(const RecordingTask& task) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    if (m_tasks.find(task.task_id) != m_tasks.end()) {
        return false; // 任务已存在
    }
    
    TaskInfo info;
    info.task = task;
    info.state = TaskState::STOPPED;
    info.total_recorded_bytes = 0;
    info.total_recorded_duration = 0;
    
    m_tasks[task.task_id] = info;
    
    return true;
}

bool RecordingManager::RemoveTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    // 停止任务
    if (it->second.state == TaskState::RUNNING) {
        StopTask(task_id);
    }
    
    // 清理资源
    DestroySDKForTask(task_id);
    DestroyRecorderForTask(task_id);
    
    // 移除任务
    m_tasks.erase(it);
    
    return true;
}

bool RecordingManager::UpdateTask(const RecordingTask& task) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task.task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    // 如果任务正在运行，需要重启
    bool was_running = (it->second.state == TaskState::RUNNING);
    if (was_running) {
        StopTask(task.task_id);
    }
    
    it->second.task = task;
    
    if (was_running) {
        StartTask(task.task_id);
    }
    
    return true;
}

bool RecordingManager::StartTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    if (it->second.state != TaskState::STOPPED && it->second.state != TaskState::ERROR) {
        return false;
    }
    
    // 创建SDK和录像器
    if (!CreateSDKForTask(task_id) || !CreateRecorderForTask(task_id)) {
        SetTaskState(task_id, TaskState::ERROR);
        return false;
    }
    
    SetTaskState(task_id, TaskState::STARTING);
    
    return true;
}

bool RecordingManager::StopTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    if (it->second.state != TaskState::RUNNING) {
        return false;
    }
    
    SetTaskState(task_id, TaskState::STOPPING);
    
    return true;
}

bool RecordingManager::StartAllTasks() {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    bool all_success = true;
    for (auto& pair : m_tasks) {
        if (pair.second.state == TaskState::STOPPED) {
            if (!StartTask(pair.first)) {
                all_success = false;
            }
        }
    }
    
    return all_success;
}

bool RecordingManager::StopAllTasks() {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    bool all_success = true;
    for (auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            if (!StopTask(pair.first)) {
                all_success = false;
            }
        }
    }
    
    return all_success;
}

std::vector<TaskInfo> RecordingManager::GetAllTasks() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    std::vector<TaskInfo> tasks;
    for (const auto& pair : m_tasks) {
        tasks.push_back(pair.second);
    }
    
    return tasks;
}

bool RecordingManager::GetTaskInfo(const std::string& task_id, TaskInfo& info) const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    info = it->second;
    return true;
}

std::vector<std::string> RecordingManager::GetRunningTasks() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    std::vector<std::string> running_tasks;
    for (const auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            running_tasks.push_back(pair.first);
        }
    }
    
    return running_tasks;
}

std::vector<RecordingInfo> RecordingManager::GetAllRecordings() const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    return m_all_recordings;
}

std::vector<RecordingInfo> RecordingManager::GetRecordingsByDevice(const std::string& device_id) const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> device_recordings;
    for (const auto& recording : m_all_recordings) {
        auto it = recording.tags.find("device_id");
        if (it != recording.tags.end() && it->second == device_id) {
            device_recordings.push_back(recording);
        }
    }
    
    return device_recordings;
}

std::vector<RecordingInfo> RecordingManager::GetRecordingsByTimeRange(
    const std::chrono::system_clock::time_point& start,
    const std::chrono::system_clock::time_point& end) const {
    
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> range_recordings;
    for (const auto& recording : m_all_recordings) {
        if (recording.start_time >= start && recording.end_time <= end) {
            range_recordings.push_back(recording);
        }
    }
    
    return range_recordings;
}

std::vector<RecordingInfo> RecordingManager::SearchRecordings(const std::string& query) const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> search_results;
    for (const auto& recording : m_all_recordings) {
        // 在文件名中搜索
        if (recording.filename.find(query) != std::string::npos) {
            search_results.push_back(recording);
            continue;
        }
        
        // 在标签中搜索
        for (const auto& tag : recording.tags) {
            if (tag.first.find(query) != std::string::npos ||
                tag.second.find(query) != std::string::npos) {
                search_results.push_back(recording);
                break;
            }
        }
    }
    
    return search_results;
}

bool RecordingManager::DeleteRecording(const std::string& hash) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = std::find_if(m_all_recordings.begin(), m_all_recordings.end(),
        [&hash](const RecordingInfo& info) {
            return info.hash_md5 == hash || info.hash_sha256 == hash;
        });

    if (it != m_all_recordings.end()) {
        // 删除文件
        std::filesystem::remove(it->filepath);

        // 从数据库中移除
        m_all_recordings.erase(it);

        // 保存数据库
        SaveAllRecordings();

        return true;
    }

    return false;
}

bool RecordingManager::DeleteRecordingsByDevice(const std::string& device_id) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        auto tag_it = it->tags.find("device_id");
        if (tag_it != it->tags.end() && tag_it->second == device_id) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

bool RecordingManager::DeleteRecordingsByTimeRange(
    const std::chrono::system_clock::time_point& start,
    const std::chrono::system_clock::time_point& end) {

    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        if (it->start_time >= start && it->end_time <= end) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

uint64_t RecordingManager::GetTotalStorageUsed() const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    uint64_t total = 0;
    for (const auto& recording : m_all_recordings) {
        total += recording.file_size;
    }

    return total;
}

uint64_t RecordingManager::GetAvailableStorage() const {
    try {
        auto space_info = std::filesystem::space("./recordings");
        return space_info.available;
    } catch (const std::exception& e) {
        return 0;
    }
}

bool RecordingManager::CleanupOldRecordings(uint32_t days_to_keep) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto cutoff_time = std::chrono::system_clock::now() - std::chrono::hours(24 * days_to_keep);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        if (it->start_time < cutoff_time) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

bool RecordingManager::CleanupByStorageLimit(uint64_t max_storage_bytes) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    uint64_t current_usage = 0;
    for (const auto& recording : m_all_recordings) {
        current_usage += recording.file_size;
    }

    if (current_usage <= max_storage_bytes) {
        return true; // 不需要清理
    }

    // 按时间排序，删除最旧的录像
    std::sort(m_all_recordings.begin(), m_all_recordings.end(),
        [](const RecordingInfo& a, const RecordingInfo& b) {
            return a.start_time < b.start_time;
        });

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end() && current_usage > max_storage_bytes) {
        current_usage -= it->file_size;

        // 删除文件
        std::filesystem::remove(it->filepath);

        // 从数据库中移除
        it = m_all_recordings.erase(it);
        deleted_any = true;
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

RecordingManager::Statistics RecordingManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    std::lock_guard<std::mutex> recordings_lock(m_recordings_mutex);

    Statistics stats;
    stats.total_tasks = m_tasks.size();
    stats.running_tasks = 0;
    stats.total_recordings = m_all_recordings.size();
    stats.total_storage_used = 0;
    stats.recordings_today = 0;
    stats.recordings_this_week = 0;
    stats.recordings_this_month = 0;

    // 统计运行中的任务
    for (const auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            stats.running_tasks++;
        }
    }

    // 统计录像信息
    auto now = std::chrono::system_clock::now();
    auto today_start = std::chrono::time_point_cast<std::chrono::days>(now);
    auto week_start = today_start - std::chrono::days(7);
    auto month_start = today_start - std::chrono::days(30);

    for (const auto& recording : m_all_recordings) {
        stats.total_storage_used += recording.file_size;

        if (recording.start_time >= today_start) {
            stats.recordings_today++;
        }
        if (recording.start_time >= week_start) {
            stats.recordings_this_week++;
        }
        if (recording.start_time >= month_start) {
            stats.recordings_this_month++;
        }
    }

    return stats;
}

bool RecordingManager::SaveConfiguration(const std::string& config_file) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);

    Json::Value root;
    Json::Value tasks(Json::arrayValue);

    for (const auto& pair : m_tasks) {
        const auto& task = pair.second.task;
        Json::Value task_json;

        task_json["task_id"] = task.task_id;
        task_json["device_id"] = task.device_id;
        task_json["continuous_recording"] = task.continuous_recording;
        task_json["motion_detection"] = task.motion_detection;
        task_json["alarm_trigger"] = task.alarm_trigger;
        task_json["pre_record_seconds"] = task.pre_record_seconds;
        task_json["post_record_seconds"] = task.post_record_seconds;

        // 芯片配置
        Json::Value chip_config;
        chip_config["ip_address"] = task.chip_config.ip_address;
        chip_config["port"] = task.chip_config.port;
        chip_config["username"] = task.chip_config.username;
        chip_config["password"] = task.chip_config.password;
        chip_config["video_channel"] = task.chip_config.video_channel;
        chip_config["video_stream"] = task.chip_config.video_stream;
        chip_config["enable_audio"] = task.chip_config.enable_audio;
        task_json["chip_config"] = chip_config;

        // 录像配置
        Json::Value recording_config;
        recording_config["output_dir"] = task.recording_config.output_dir;
        recording_config["max_file_size"] = (Json::UInt64)task.recording_config.max_file_size;
        recording_config["max_duration"] = task.recording_config.max_duration;
        recording_config["enable_hash_tagging"] = task.recording_config.enable_hash_tagging;
        recording_config["file_prefix"] = task.recording_config.file_prefix;
        task_json["recording_config"] = recording_config;

        // 标签
        Json::Value tags;
        for (const auto& tag : task.tags) {
            tags[tag.first] = tag.second;
        }
        task_json["tags"] = tags;

        tasks.append(task_json);
    }

    root["tasks"] = tasks;
    root["storage_warning_threshold"] = (Json::UInt64)m_storage_warning_threshold;
    root["cleanup_interval_hours"] = m_cleanup_interval_hours;

    std::ofstream file(config_file);
    if (file.is_open()) {
        file << root;
        return true;
    }

    return false;
}

bool RecordingManager::LoadConfiguration(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        return false;
    }

    Json::Value root;
    file >> root;

    if (root.isMember("storage_warning_threshold")) {
        m_storage_warning_threshold = root["storage_warning_threshold"].asUInt64();
    }

    if (root.isMember("cleanup_interval_hours")) {
        m_cleanup_interval_hours = root["cleanup_interval_hours"].asUInt();
    }

    if (!root.isMember("tasks")) {
        return true;
    }

    const Json::Value& tasks = root["tasks"];
    for (const auto& task_json : tasks) {
        RecordingTask task;

        task.task_id = task_json["task_id"].asString();
        task.device_id = task_json["device_id"].asString();
        task.continuous_recording = task_json["continuous_recording"].asBool();
        task.motion_detection = task_json["motion_detection"].asBool();
        task.alarm_trigger = task_json["alarm_trigger"].asBool();
        task.pre_record_seconds = task_json["pre_record_seconds"].asInt();
        task.post_record_seconds = task_json["post_record_seconds"].asInt();

        // 芯片配置
        if (task_json.isMember("chip_config")) {
            const Json::Value& chip_config = task_json["chip_config"];
            task.chip_config.ip_address = chip_config["ip_address"].asString();
            task.chip_config.port = chip_config["port"].asInt();
            task.chip_config.username = chip_config["username"].asString();
            task.chip_config.password = chip_config["password"].asString();
            task.chip_config.video_channel = chip_config["video_channel"].asInt();
            task.chip_config.video_stream = chip_config["video_stream"].asInt();
            task.chip_config.enable_audio = chip_config["enable_audio"].asBool();
        }

        // 录像配置
        if (task_json.isMember("recording_config")) {
            const Json::Value& recording_config = task_json["recording_config"];
            task.recording_config.output_dir = recording_config["output_dir"].asString();
            task.recording_config.max_file_size = recording_config["max_file_size"].asUInt64();
            task.recording_config.max_duration = recording_config["max_duration"].asUInt();
            task.recording_config.enable_hash_tagging = recording_config["enable_hash_tagging"].asBool();
            task.recording_config.file_prefix = recording_config["file_prefix"].asString();
        }

        // 标签
        if (task_json.isMember("tags")) {
            const Json::Value& tags = task_json["tags"];
            for (const auto& key : tags.getMemberNames()) {
                task.tags[key] = tags[key].asString();
            }
        }

        AddTask(task);
    }

    return true;
}

void RecordingManager::ManagerThread() {
    auto last_cleanup_time = std::chrono::steady_clock::now();

    while (m_running) {
        // 处理所有任务
        std::vector<std::string> task_ids;
        {
            std::lock_guard<std::mutex> lock(m_tasks_mutex);
            for (const auto& pair : m_tasks) {
                task_ids.push_back(pair.first);
            }
        }

        for (const auto& task_id : task_ids) {
            ProcessTask(task_id);
        }

        // 定期检查存储使用情况
        auto now = std::chrono::steady_clock::now();
        if (now - last_cleanup_time >= std::chrono::hours(m_cleanup_interval_hours)) {
            CheckStorageUsage();
            last_cleanup_time = now;
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}
