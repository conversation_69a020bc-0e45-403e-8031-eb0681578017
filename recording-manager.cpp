#include "recording-manager.h"
#include <iostream>
#include <fstream>
#include <filesystem>
#include <algorithm>
#include <json/json.h>

RecordingManager::RecordingManager()
    : m_recordings_db_file("./recordings/recordings_db.json")
    , m_config_file("./config/recording_config.json")
    , m_storage_warning_threshold(80 * 1024 * 1024 * 1024ULL) // 80GB
    , m_cleanup_interval_hours(24) {
    
    // 创建必要的目录
    std::filesystem::create_directories("./recordings");
    std::filesystem::create_directories("./config");
    
    // 加载所有录像记录
    LoadAllRecordings();
}

RecordingManager::~RecordingManager() {
    Stop();
}

bool RecordingManager::Start() {
    if (m_running) {
        return true;
    }
    
    m_running = true;
    m_manager_thread = std::thread(&RecordingManager::ManagerThread, this);
    
    return true;
}

bool RecordingManager::Stop() {
    if (!m_running) {
        return true;
    }
    
    // 停止所有任务
    StopAllTasks();
    
    // 停止管理线程
    m_running = false;
    if (m_manager_thread.joinable()) {
        m_manager_thread.join();
    }
    
    // 保存录像数据库
    SaveAllRecordings();
    
    return true;
}

bool RecordingManager::AddTask(const RecordingTask& task) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    if (m_tasks.find(task.task_id) != m_tasks.end()) {
        return false; // 任务已存在
    }
    
    TaskInfo info;
    info.task = task;
    info.state = TaskState::STOPPED;
    info.total_recorded_bytes = 0;
    info.total_recorded_duration = 0;
    
    m_tasks[task.task_id] = info;
    
    return true;
}

bool RecordingManager::RemoveTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    // 停止任务
    if (it->second.state == TaskState::RUNNING) {
        StopTask(task_id);
    }
    
    // 清理资源
    DestroySDKForTask(task_id);
    DestroyRecorderForTask(task_id);
    
    // 移除任务
    m_tasks.erase(it);
    
    return true;
}

bool RecordingManager::UpdateTask(const RecordingTask& task) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task.task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    // 如果任务正在运行，需要重启
    bool was_running = (it->second.state == TaskState::RUNNING);
    if (was_running) {
        StopTask(task.task_id);
    }
    
    it->second.task = task;
    
    if (was_running) {
        StartTask(task.task_id);
    }
    
    return true;
}

bool RecordingManager::StartTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    if (it->second.state != TaskState::STOPPED && it->second.state != TaskState::ERROR) {
        return false;
    }
    
    // 创建SDK和录像器
    if (!CreateSDKForTask(task_id) || !CreateRecorderForTask(task_id)) {
        SetTaskState(task_id, TaskState::ERROR);
        return false;
    }
    
    SetTaskState(task_id, TaskState::STARTING);
    
    return true;
}

bool RecordingManager::StopTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    if (it->second.state != TaskState::RUNNING) {
        return false;
    }
    
    SetTaskState(task_id, TaskState::STOPPING);
    
    return true;
}

bool RecordingManager::StartAllTasks() {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    bool all_success = true;
    for (auto& pair : m_tasks) {
        if (pair.second.state == TaskState::STOPPED) {
            if (!StartTask(pair.first)) {
                all_success = false;
            }
        }
    }
    
    return all_success;
}

bool RecordingManager::StopAllTasks() {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    bool all_success = true;
    for (auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            if (!StopTask(pair.first)) {
                all_success = false;
            }
        }
    }
    
    return all_success;
}

std::vector<TaskInfo> RecordingManager::GetAllTasks() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    std::vector<TaskInfo> tasks;
    for (const auto& pair : m_tasks) {
        tasks.push_back(pair.second);
    }
    
    return tasks;
}

bool RecordingManager::GetTaskInfo(const std::string& task_id, TaskInfo& info) const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }
    
    info = it->second;
    return true;
}

std::vector<std::string> RecordingManager::GetRunningTasks() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    
    std::vector<std::string> running_tasks;
    for (const auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            running_tasks.push_back(pair.first);
        }
    }
    
    return running_tasks;
}

std::vector<RecordingInfo> RecordingManager::GetAllRecordings() const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    return m_all_recordings;
}

std::vector<RecordingInfo> RecordingManager::GetRecordingsByDevice(const std::string& device_id) const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> device_recordings;
    for (const auto& recording : m_all_recordings) {
        auto it = recording.tags.find("device_id");
        if (it != recording.tags.end() && it->second == device_id) {
            device_recordings.push_back(recording);
        }
    }
    
    return device_recordings;
}

std::vector<RecordingInfo> RecordingManager::GetRecordingsByTimeRange(
    const std::chrono::system_clock::time_point& start,
    const std::chrono::system_clock::time_point& end) const {
    
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> range_recordings;
    for (const auto& recording : m_all_recordings) {
        if (recording.start_time >= start && recording.end_time <= end) {
            range_recordings.push_back(recording);
        }
    }
    
    return range_recordings;
}

std::vector<RecordingInfo> RecordingManager::SearchRecordings(const std::string& query) const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    
    std::vector<RecordingInfo> search_results;
    for (const auto& recording : m_all_recordings) {
        // 在文件名中搜索
        if (recording.filename.find(query) != std::string::npos) {
            search_results.push_back(recording);
            continue;
        }
        
        // 在标签中搜索
        for (const auto& tag : recording.tags) {
            if (tag.first.find(query) != std::string::npos ||
                tag.second.find(query) != std::string::npos) {
                search_results.push_back(recording);
                break;
            }
        }
    }
    
    return search_results;
}

bool RecordingManager::DeleteRecording(const std::string& hash) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = std::find_if(m_all_recordings.begin(), m_all_recordings.end(),
        [&hash](const RecordingInfo& info) {
            return info.hash_md5 == hash || info.hash_sha256 == hash;
        });

    if (it != m_all_recordings.end()) {
        // 删除文件
        std::filesystem::remove(it->filepath);

        // 从数据库中移除
        m_all_recordings.erase(it);

        // 保存数据库
        SaveAllRecordings();

        return true;
    }

    return false;
}

bool RecordingManager::DeleteRecordingsByDevice(const std::string& device_id) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        auto tag_it = it->tags.find("device_id");
        if (tag_it != it->tags.end() && tag_it->second == device_id) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

bool RecordingManager::DeleteRecordingsByTimeRange(
    const std::chrono::system_clock::time_point& start,
    const std::chrono::system_clock::time_point& end) {

    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        if (it->start_time >= start && it->end_time <= end) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

uint64_t RecordingManager::GetTotalStorageUsed() const {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    uint64_t total = 0;
    for (const auto& recording : m_all_recordings) {
        total += recording.file_size;
    }

    return total;
}

uint64_t RecordingManager::GetAvailableStorage() const {
    try {
        auto space_info = std::filesystem::space("./recordings");
        return space_info.available;
    } catch (const std::exception& e) {
        return 0;
    }
}

bool RecordingManager::CleanupOldRecordings(uint32_t days_to_keep) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    auto cutoff_time = std::chrono::system_clock::now() - std::chrono::hours(24 * days_to_keep);

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end()) {
        if (it->start_time < cutoff_time) {
            // 删除文件
            std::filesystem::remove(it->filepath);

            // 从数据库中移除
            it = m_all_recordings.erase(it);
            deleted_any = true;
        } else {
            ++it;
        }
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

bool RecordingManager::CleanupByStorageLimit(uint64_t max_storage_bytes) {
    std::lock_guard<std::mutex> lock(m_recordings_mutex);

    uint64_t current_usage = 0;
    for (const auto& recording : m_all_recordings) {
        current_usage += recording.file_size;
    }

    if (current_usage <= max_storage_bytes) {
        return true; // 不需要清理
    }

    // 按时间排序，删除最旧的录像
    std::sort(m_all_recordings.begin(), m_all_recordings.end(),
        [](const RecordingInfo& a, const RecordingInfo& b) {
            return a.start_time < b.start_time;
        });

    auto it = m_all_recordings.begin();
    bool deleted_any = false;

    while (it != m_all_recordings.end() && current_usage > max_storage_bytes) {
        current_usage -= it->file_size;

        // 删除文件
        std::filesystem::remove(it->filepath);

        // 从数据库中移除
        it = m_all_recordings.erase(it);
        deleted_any = true;
    }

    if (deleted_any) {
        SaveAllRecordings();
    }

    return deleted_any;
}

RecordingManager::Statistics RecordingManager::GetStatistics() const {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);
    std::lock_guard<std::mutex> recordings_lock(m_recordings_mutex);

    Statistics stats;
    stats.total_tasks = m_tasks.size();
    stats.running_tasks = 0;
    stats.total_recordings = m_all_recordings.size();
    stats.total_storage_used = 0;
    stats.recordings_today = 0;
    stats.recordings_this_week = 0;
    stats.recordings_this_month = 0;

    // 统计运行中的任务
    for (const auto& pair : m_tasks) {
        if (pair.second.state == TaskState::RUNNING) {
            stats.running_tasks++;
        }
    }

    // 统计录像信息
    auto now = std::chrono::system_clock::now();
    auto today_start = std::chrono::time_point_cast<std::chrono::days>(now);
    auto week_start = today_start - std::chrono::days(7);
    auto month_start = today_start - std::chrono::days(30);

    for (const auto& recording : m_all_recordings) {
        stats.total_storage_used += recording.file_size;

        if (recording.start_time >= today_start) {
            stats.recordings_today++;
        }
        if (recording.start_time >= week_start) {
            stats.recordings_this_week++;
        }
        if (recording.start_time >= month_start) {
            stats.recordings_this_month++;
        }
    }

    return stats;
}

bool RecordingManager::SaveConfiguration(const std::string& config_file) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);

    Json::Value root;
    Json::Value tasks(Json::arrayValue);

    for (const auto& pair : m_tasks) {
        const auto& task = pair.second.task;
        Json::Value task_json;

        task_json["task_id"] = task.task_id;
        task_json["device_id"] = task.device_id;
        task_json["continuous_recording"] = task.continuous_recording;
        task_json["motion_detection"] = task.motion_detection;
        task_json["alarm_trigger"] = task.alarm_trigger;
        task_json["pre_record_seconds"] = task.pre_record_seconds;
        task_json["post_record_seconds"] = task.post_record_seconds;

        // 芯片配置
        Json::Value chip_config;
        chip_config["ip_address"] = task.chip_config.ip_address;
        chip_config["port"] = task.chip_config.port;
        chip_config["username"] = task.chip_config.username;
        chip_config["password"] = task.chip_config.password;
        chip_config["video_channel"] = task.chip_config.video_channel;
        chip_config["video_stream"] = task.chip_config.video_stream;
        chip_config["enable_audio"] = task.chip_config.enable_audio;
        task_json["chip_config"] = chip_config;

        // 录像配置
        Json::Value recording_config;
        recording_config["output_dir"] = task.recording_config.output_dir;
        recording_config["max_file_size"] = (Json::UInt64)task.recording_config.max_file_size;
        recording_config["max_duration"] = task.recording_config.max_duration;
        recording_config["enable_hash_tagging"] = task.recording_config.enable_hash_tagging;
        recording_config["file_prefix"] = task.recording_config.file_prefix;
        task_json["recording_config"] = recording_config;

        // 标签
        Json::Value tags;
        for (const auto& tag : task.tags) {
            tags[tag.first] = tag.second;
        }
        task_json["tags"] = tags;

        tasks.append(task_json);
    }

    root["tasks"] = tasks;
    root["storage_warning_threshold"] = (Json::UInt64)m_storage_warning_threshold;
    root["cleanup_interval_hours"] = m_cleanup_interval_hours;

    std::ofstream file(config_file);
    if (file.is_open()) {
        file << root;
        return true;
    }

    return false;
}

bool RecordingManager::LoadConfiguration(const std::string& config_file) {
    std::ifstream file(config_file);
    if (!file.is_open()) {
        return false;
    }

    Json::Value root;
    file >> root;

    if (root.isMember("storage_warning_threshold")) {
        m_storage_warning_threshold = root["storage_warning_threshold"].asUInt64();
    }

    if (root.isMember("cleanup_interval_hours")) {
        m_cleanup_interval_hours = root["cleanup_interval_hours"].asUInt();
    }

    if (!root.isMember("tasks")) {
        return true;
    }

    const Json::Value& tasks = root["tasks"];
    for (const auto& task_json : tasks) {
        RecordingTask task;

        task.task_id = task_json["task_id"].asString();
        task.device_id = task_json["device_id"].asString();
        task.continuous_recording = task_json["continuous_recording"].asBool();
        task.motion_detection = task_json["motion_detection"].asBool();
        task.alarm_trigger = task_json["alarm_trigger"].asBool();
        task.pre_record_seconds = task_json["pre_record_seconds"].asInt();
        task.post_record_seconds = task_json["post_record_seconds"].asInt();

        // 芯片配置
        if (task_json.isMember("chip_config")) {
            const Json::Value& chip_config = task_json["chip_config"];
            task.chip_config.ip_address = chip_config["ip_address"].asString();
            task.chip_config.port = chip_config["port"].asInt();
            task.chip_config.username = chip_config["username"].asString();
            task.chip_config.password = chip_config["password"].asString();
            task.chip_config.video_channel = chip_config["video_channel"].asInt();
            task.chip_config.video_stream = chip_config["video_stream"].asInt();
            task.chip_config.enable_audio = chip_config["enable_audio"].asBool();
        }

        // 录像配置
        if (task_json.isMember("recording_config")) {
            const Json::Value& recording_config = task_json["recording_config"];
            task.recording_config.output_dir = recording_config["output_dir"].asString();
            task.recording_config.max_file_size = recording_config["max_file_size"].asUInt64();
            task.recording_config.max_duration = recording_config["max_duration"].asUInt();
            task.recording_config.enable_hash_tagging = recording_config["enable_hash_tagging"].asBool();
            task.recording_config.file_prefix = recording_config["file_prefix"].asString();
        }

        // 标签
        if (task_json.isMember("tags")) {
            const Json::Value& tags = task_json["tags"];
            for (const auto& key : tags.getMemberNames()) {
                task.tags[key] = tags[key].asString();
            }
        }

        AddTask(task);
    }

    return true;
}

void RecordingManager::ManagerThread() {
    auto last_cleanup_time = std::chrono::steady_clock::now();

    while (m_running) {
        // 处理所有任务
        std::vector<std::string> task_ids;
        {
            std::lock_guard<std::mutex> lock(m_tasks_mutex);
            for (const auto& pair : m_tasks) {
                task_ids.push_back(pair.first);
            }
        }

        for (const auto& task_id : task_ids) {
            ProcessTask(task_id);
        }

        // 定期检查存储使用情况
        auto now = std::chrono::steady_clock::now();
        if (now - last_cleanup_time >= std::chrono::hours(m_cleanup_interval_hours)) {
            CheckStorageUsage();
            last_cleanup_time = now;
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

void RecordingManager::ProcessTask(const std::string& task_id) {
    std::lock_guard<std::mutex> lock(m_tasks_mutex);

    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return;
    }

    TaskInfo& task_info = it->second;

    switch (task_info.state) {
        case TaskState::STARTING:
            // 尝试连接设备并启动录像
            if (CreateSDKForTask(task_id) && CreateRecorderForTask(task_id)) {
                auto sdk_it = m_sdks.find(task_id);
                auto recorder_it = m_recorders.find(task_id);

                if (sdk_it != m_sdks.end() && recorder_it != m_recorders.end()) {
                    // 连接设备
                    if (sdk_it->second->Connect()) {
                        // 启动流
                        if (sdk_it->second->StartStream()) {
                            // 启动录像
                            if (recorder_it->second->StartRecording(task_id)) {
                                SetTaskState(task_id, TaskState::RUNNING);
                                task_info.last_start_time = std::chrono::system_clock::now();
                            } else {
                                OnTaskError(task_id, "Failed to start recording");
                                SetTaskState(task_id, TaskState::ERROR);
                            }
                        } else {
                            OnTaskError(task_id, "Failed to start stream");
                            SetTaskState(task_id, TaskState::ERROR);
                        }
                    } else {
                        OnTaskError(task_id, "Failed to connect to device");
                        SetTaskState(task_id, TaskState::ERROR);
                    }
                }
            } else {
                OnTaskError(task_id, "Failed to create SDK or recorder");
                SetTaskState(task_id, TaskState::ERROR);
            }
            break;

        case TaskState::RUNNING:
            // 检查是否需要停止录像
            if (!ShouldStartRecording(task_info.task)) {
                SetTaskState(task_id, TaskState::STOPPING);
            }
            break;

        case TaskState::STOPPING:
            // 停止录像和流
            {
                auto sdk_it = m_sdks.find(task_id);
                auto recorder_it = m_recorders.find(task_id);

                if (recorder_it != m_recorders.end()) {
                    recorder_it->second->StopRecording();
                }

                if (sdk_it != m_sdks.end()) {
                    sdk_it->second->StopStream();
                    sdk_it->second->Disconnect();
                }

                SetTaskState(task_id, TaskState::STOPPED);
                task_info.last_stop_time = std::chrono::system_clock::now();
            }
            break;

        case TaskState::STOPPED:
            // 检查是否需要重新启动
            if (ShouldStartRecording(task_info.task)) {
                SetTaskState(task_id, TaskState::STARTING);
            }
            break;

        case TaskState::ERROR:
            // 错误状态，等待一段时间后重试
            {
                auto now = std::chrono::system_clock::now();
                auto error_duration = now - task_info.last_stop_time;
                if (error_duration >= std::chrono::minutes(5)) { // 5分钟后重试
                    SetTaskState(task_id, TaskState::STOPPED);
                }
            }
            break;

        default:
            break;
    }
}

bool RecordingManager::ShouldStartRecording(const RecordingTask& task) const {
    if (task.continuous_recording) {
        return IsInTimeRange(task) && IsInWeekday(task);
    }

    // 其他触发条件（移动侦测、报警等）
    // 这里可以添加更复杂的逻辑

    return false;
}

bool RecordingManager::IsInTimeRange(const RecordingTask& task) const {
    if (task.time_ranges.empty()) {
        return true; // 没有时间限制
    }

    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    int current_hour = tm.tm_hour;
    int current_min = tm.tm_min;
    int current_time = current_hour * 60 + current_min;

    for (const auto& range : task.time_ranges) {
        int start_time = range.first * 60 + range.second;
        int end_time = range.first * 60 + range.second; // 这里应该有结束时间

        if (current_time >= start_time && current_time <= end_time) {
            return true;
        }
    }

    return false;
}

bool RecordingManager::IsInWeekday(const RecordingTask& task) const {
    if (task.weekdays.empty()) {
        return true; // 没有星期限制
    }

    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto tm = *std::localtime(&time_t);

    int current_weekday = tm.tm_wday; // 0=周日, 1=周一, ...

    return std::find(task.weekdays.begin(), task.weekdays.end(), current_weekday) != task.weekdays.end();
}

void RecordingManager::SetTaskState(const std::string& task_id, TaskState new_state) {
    auto it = m_tasks.find(task_id);
    if (it != m_tasks.end()) {
        TaskState old_state = it->second.state;
        it->second.state = new_state;

        if (m_task_state_callback) {
            m_task_state_callback(task_id, old_state, new_state);
        }
    }
}

void RecordingManager::OnRecordingComplete(const std::string& task_id, const RecordingInfo& info) {
    // 更新任务统计信息
    {
        std::lock_guard<std::mutex> lock(m_tasks_mutex);
        auto it = m_tasks.find(task_id);
        if (it != m_tasks.end()) {
            it->second.total_recorded_bytes += info.file_size;
            it->second.total_recorded_duration += info.duration;
            it->second.recordings.push_back(info);
        }
    }

    // 添加到全局录像数据库
    {
        std::lock_guard<std::mutex> lock(m_recordings_mutex);
        m_all_recordings.push_back(info);
        SaveAllRecordings();
    }

    if (m_recording_complete_callback) {
        m_recording_complete_callback(task_id, info);
    }
}

void RecordingManager::OnTaskError(const std::string& task_id, const std::string& error) {
    {
        std::lock_guard<std::mutex> lock(m_tasks_mutex);
        auto it = m_tasks.find(task_id);
        if (it != m_tasks.end()) {
            it->second.error_message = error;
        }
    }

    if (m_error_callback) {
        m_error_callback(task_id, error);
    }
}

void RecordingManager::CheckStorageUsage() {
    uint64_t total_used = GetTotalStorageUsed();
    uint64_t available = GetAvailableStorage();
    uint64_t total = total_used + available;

    if (total > 0) {
        double usage_percent = (double)total_used / total;
        if (usage_percent > 0.8) { // 80%警告阈值
            if (m_storage_warning_callback) {
                m_storage_warning_callback(total_used, total);
            }

            // 自动清理旧录像
            CleanupOldRecordings(30); // 保留30天
        }
    }
}

void RecordingManager::LoadAllRecordings() {
    std::ifstream file(m_recordings_db_file);
    if (!file.is_open()) {
        return;
    }

    Json::Value root;
    file >> root;

    if (!root.isMember("recordings")) {
        return;
    }

    std::lock_guard<std::mutex> lock(m_recordings_mutex);
    m_all_recordings.clear();

    const Json::Value& recordings = root["recordings"];
    for (const auto& record : recordings) {
        RecordingInfo info;
        info.filename = record["filename"].asString();
        info.filepath = record["filepath"].asString();
        info.hash_md5 = record["hash_md5"].asString();
        info.hash_sha256 = record["hash_sha256"].asString();
        info.file_size = record["file_size"].asUInt64();
        info.duration = record["duration"].asUInt();

        auto start_time_t = record["start_time"].asUInt64();
        auto end_time_t = record["end_time"].asUInt64();
        info.start_time = std::chrono::system_clock::from_time_t(start_time_t);
        info.end_time = std::chrono::system_clock::from_time_t(end_time_t);

        const Json::Value& tags = record["tags"];
        for (const auto& key : tags.getMemberNames()) {
            info.tags[key] = tags[key].asString();
        }

        m_all_recordings.push_back(info);
    }
}

void RecordingManager::SaveAllRecordings() {
    Json::Value root;
    Json::Value recordings(Json::arrayValue);

    for (const auto& info : m_all_recordings) {
        Json::Value record;
        record["filename"] = info.filename;
        record["filepath"] = info.filepath;
        record["hash_md5"] = info.hash_md5;
        record["hash_sha256"] = info.hash_sha256;
        record["file_size"] = (Json::UInt64)info.file_size;
        record["duration"] = info.duration;

        auto start_time_t = std::chrono::system_clock::to_time_t(info.start_time);
        auto end_time_t = std::chrono::system_clock::to_time_t(info.end_time);
        record["start_time"] = (Json::UInt64)start_time_t;
        record["end_time"] = (Json::UInt64)end_time_t;

        Json::Value tags;
        for (const auto& tag : info.tags) {
            tags[tag.first] = tag.second;
        }
        record["tags"] = tags;

        recordings.append(record);
    }

    root["recordings"] = recordings;

    std::ofstream file(m_recordings_db_file);
    if (file.is_open()) {
        file << root;
    }
}

bool RecordingManager::CreateSDKForTask(const std::string& task_id) {
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }

    const RecordingTask& task = it->second.task;

    // 创建对应的SDK
    auto sdk = SecurityChipSDKFactory::CreateSDK(task.sdk_type, task.chip_config);
    if (!sdk) {
        return false;
    }

    // 设置回调
    sdk->SetStreamDataCallback([this, task_id](const MediaFrame& frame) {
        OnStreamData(task_id, frame);
    });

    sdk->SetConnectionStatusCallback([this, task_id](bool connected, const std::string& error) {
        OnConnectionStatus(task_id, connected, error);
    });

    m_sdks[task_id] = std::move(sdk);
    return true;
}

bool RecordingManager::CreateRecorderForTask(const std::string& task_id) {
    auto it = m_tasks.find(task_id);
    if (it == m_tasks.end()) {
        return false;
    }

    const RecordingTask& task = it->second.task;

    // 创建录像器
    auto recorder = std::make_unique<MP4Recorder>(task.recording_config);

    // 设置回调
    recorder->SetRecordingStartCallback([this, task_id](const std::string& filename) {
        // 录像开始回调
    });

    recorder->SetRecordingStopCallback([this, task_id](const RecordingInfo& info) {
        OnRecordingComplete(task_id, info);
    });

    recorder->SetErrorCallback([this, task_id](const std::string& error) {
        OnTaskError(task_id, "Recording error: " + error);
    });

    // 添加任务标签
    for (const auto& tag : task.tags) {
        recorder->AddTag(tag.first, tag.second);
    }
    recorder->AddTag("task_id", task_id);
    recorder->AddTag("device_id", task.device_id);

    m_recorders[task_id] = std::move(recorder);
    return true;
}

void RecordingManager::DestroySDKForTask(const std::string& task_id) {
    auto it = m_sdks.find(task_id);
    if (it != m_sdks.end()) {
        it->second->StopStream();
        it->second->Disconnect();
        m_sdks.erase(it);
    }
}

void RecordingManager::DestroyRecorderForTask(const std::string& task_id) {
    auto it = m_recorders.find(task_id);
    if (it != m_recorders.end()) {
        it->second->StopRecording();
        m_recorders.erase(it);
    }
}

void RecordingManager::OnStreamData(const std::string& task_id, const MediaFrame& frame) {
    auto recorder_it = m_recorders.find(task_id);
    if (recorder_it != m_recorders.end()) {
        if (frame.type == MediaFrame::VIDEO_H264) {
            recorder_it->second->WriteVideoFrame(frame);
        } else if (frame.type == MediaFrame::AUDIO_AAC) {
            recorder_it->second->WriteAudioFrame(frame);
        }
    }
}

void RecordingManager::OnConnectionStatus(const std::string& task_id, bool connected, const std::string& error) {
    if (!connected) {
        OnTaskError(task_id, "Connection lost: " + error);

        // 设置任务为错误状态
        std::lock_guard<std::mutex> lock(m_tasks_mutex);
        auto it = m_tasks.find(task_id);
        if (it != m_tasks.end() && it->second.state == TaskState::RUNNING) {
            SetTaskState(task_id, TaskState::ERROR);
        }
    }
}
