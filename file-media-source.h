#ifndef FILE_MEDIA_SOURCE_H
#define FILE_MEDIA_SOURCE_H

#include "MediaSourceManager.h"
#include <thread>
#include <list>

class FileMediaSource : public MediaSource {
public:
    FileMediaSource(const std::string& path, const std::string& filePath);
    ~FileMediaSource();
    
    // MediaSource接口
    const std::string& GetStreamPath() const override { return m_path; }
    const MediaInfo& GetMediaInfo() const override { return m_mediaInfo; }
    bool IsLive() const override { return false; }
    
    void AddSink(std::weak_ptr<MediaSession> session) override;
    void RemoveSink(std::weak_ptr<MediaSession> session) override;
    void PushPacket(const MediaPacket& packet) override;
    
    // 文件播放控制
    void Play();
    void Pause();
    void Seek(uint32_t timestamp);
    
private:
    std::string m_path;
    std::string m_filePath;
    MediaInfo m_mediaInfo;
    mutable std::mutex m_mutex;
    
    // 会话管理
    std::list<std::weak_ptr<MediaSession>> m_sessions;
    
    // 播放控制
    std::atomic<bool> m_playing{false};
    std::atomic<bool> m_running{false};
    std::thread m_playThread;
    uint32_t m_currentTimestamp = 0;
    
    // 文件读取
    void* m_demuxer = nullptr;  // 根据文件类型选择解复用器
    
    void PlayThread();
    void InitializeDemuxer();
    void CloseDemuxer();
    bool ReadPacket(MediaPacket& packet);
    void CleanupSessions();
};

#endif // FILE_MEDIA_SOURCE_H