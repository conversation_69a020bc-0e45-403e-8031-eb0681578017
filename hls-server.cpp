#include "HlsServer.h"
#include "hls-param.h"
#include "aio-worker.h"
#include <iostream>
#include <sstream>
#include <cstring>

// HlsPlaylist实现
HlsPlaylist::HlsPlaylist(const std::string& path, std::shared_ptr<MediaSourceManager> sourceManager)
    : m_path(path), m_sourceManager(sourceManager), m_m3u8(nullptr), m_hls(nullptr) {
}

HlsPlaylist::~HlsPlaylist() {
    Stop();
}

void HlsPlaylist::Start() {
    if (m_running) {
        return;
    }
    
    m_running = true;
    
    // 查找媒体源
    m_mediaSource = m_sourceManager->FindSource(m_path);
    if (!m_mediaSource) {
        m_mediaSource = m_sourceManager->CreateOrGetSource(m_path, false);
    }
    
    if (m_mediaSource) {
        m_mediaInfo = m_mediaSource->GetMediaInfo();
        InitHLS();
        m_mediaSource->AddSink(weak_from_this());
    }
}

void HlsPlaylist::Stop() {
    m_running = false;
    
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
        m_mediaSource.reset();
    }
    
    if (m_hls) {
        hls_media_destroy(m_hls);
        m_hls = nullptr;
    }
    
    if (m_m3u8) {
        hls_m3u8_destroy(m_m3u8);
        m_m3u8 = nullptr;
    }
    
    {
        std::lock_guard<std::mutex> lock(m_segmentMutex);
        m_segments.clear();
    }
}

void HlsPlaylist::OnMediaPacket(const MediaPacket& packet) {
    if (!m_running || !m_hls) {
        return;
    }
    
    int stream_type = 0;
    int flags = 0;
    
    switch (packet.type) {
    case MediaPacket::VIDEO_H264:
        stream_type = PSI_STREAM_H264;
        flags = packet.keyframe ? HLS_FLAGS_KEYFRAME : 0;
        break;
        
    case MediaPacket::VIDEO_H265:
        stream_type = PSI_STREAM_H265;
        flags = packet.keyframe ? HLS_FLAGS_KEYFRAME : 0;
        break;
        
    case MediaPacket::AUDIO_AAC:
        stream_type = PSI_STREAM_AAC;
        break;
        
    default:
        return;
    }
    
    hls_media_input(m_hls, stream_type, packet.data.data(), packet.data.size(), 
                    packet.timestamp, packet.timestamp, flags);
}

void HlsPlaylist::OnMediaInfo(const MediaInfo& info) {
    m_mediaInfo = info;
    InitHLS();
}

void HlsPlaylist::InitHLS() {
    if (m_hls) {
        hls_media_destroy(m_hls);
    }
    
    if (m_m3u8) {
        hls_m3u8_destroy(m_m3u8);
    }
    
    // 创建M3U8播放列表
    m_m3u8 = hls_m3u8_create(SEGMENT_COUNT, 3);
    
    // 创建HLS媒体处理器
    m_hls = hls_media_create(SEGMENT_DURATION * 1000, OnHlsSegment, this);
}

int HlsPlaylist::OnHlsSegment(void* param, const void* data, size_t bytes, 
                              int64_t pts, int64_t dts, int64_t duration) {
    HlsPlaylist* playlist = (HlsPlaylist*)param;
    
    auto segment = std::make_shared<HlsSegment>();
    segment->filename = playlist->m_path + "/" + std::to_string(playlist->m_segmentIndex++) + ".ts";
    segment->duration = duration / 1000.0; // 转换为秒
    segment->timestamp = pts;
    segment->data.assign((const uint8_t*)data, (const uint8_t*)data + bytes);
    
    // 添加到M3U8
    hls_m3u8_add(playlist->m_m3u8, segment->filename.c_str(), pts, duration, 0);
    
    // 保存片段
    {
        std::lock_guard<std::mutex> lock(playlist->m_segmentMutex);
        playlist->m_segments.push_back(segment);
        playlist->CleanupSegments();
    }
    
    return 0;
}

void HlsPlaylist::CleanupSegments() {
    // 清理过期的片段
    while (m_segments.size() > SEGMENT_COUNT + 2) {
        auto segment = m_segments.front();
        if (segment->refcount == 0) {
            m_segments.pop_front();
        } else {
            break;
        }
    }
}

std::string HlsPlaylist::GetM3U8() const {
    if (!m_m3u8) {
        return "";
    }
    
    char buffer[8 * 1024];
    hls_m3u8_playlist(m_m3u8, m_mediaSource && m_mediaSource->IsLive(), buffer, sizeof(buffer));
    return buffer;
}

std::shared_ptr<HlsSegment> HlsPlaylist::GetSegment(const std::string& filename) {
    std::lock_guard<std::mutex> lock(m_segmentMutex);
    
    for (auto& segment : m_segments) {
        if (segment->filename == filename) {
            segment->refcount++;
            return segment;
        }
    }
    
    return nullptr;
}

// HlsServer实现
HlsServer::HlsServer(std::shared_ptr<MediaSourceManager> sourceManager)
    : m_sourceManager(sourceManager), m_httpServer(nullptr) {
    aio_worker_init(4);
}

HlsServer::~HlsServer() {
    Stop();
    aio_worker_clean(4);
}

bool HlsServer::Start(int port, const std::string& path) {
    if (m_running) {
        return false;
    }
    
    m_path = path;
    
    // 创建HTTP服务器
    m_httpServer = http_server_create(NULL, port);
    if (!m_httpServer) {
        std::cerr << "Failed to create HTTP server on port " << port << std::endl;
        return false;
    }
    
    // 设置HTTP请求处理器
    http_server_set_handler(m_httpServer, OnHttpRequest, this);
    
    m_running = true;
    return true;
}

void HlsServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    if (m_httpServer) {
        http_server_destroy(m_httpServer);
        m_httpServer = nullptr;
    }
    
    // 停止所有播放列表
    {
        std::lock_guard<std::mutex> lock(m_playlistMutex);
        for (auto& pair : m_playlists) {
            pair.second->Stop();
        }
        m_playlists.clear();
    }
}

int HlsServer::OnHttpRequest(void* param, http_session_t* session, 
                             const char* method, const char* uri) {
    HlsServer* server = (HlsServer*)param;
    
    if (strcmp(method, "GET") != 0) {
        http_server_set_status_code(session, 405, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    std::string path(uri);
    
    // 检查是否是HLS请求
    if (path.find(server->m_path) != 0) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    // 移除前缀
    path = path.substr(server->m_path.length());
    if (!path.empty() && path[0] == '/') {
        path = path.substr(1);
    }
    
    // 判断请求类型
    if (path.empty() || path.back() == '/') {
        // 目录请求
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    size_t pos = path.rfind('/');
    if (pos == std::string::npos) {
        // 根目录文件
        if (path.find(".m3u8") != std::string::npos) {
            std::string streamPath = path.substr(0, path.find(".m3u8"));
            return server->HandleM3U8Request(session, streamPath);
        }
    } else {
        // 子目录文件
        std::string streamPath = path.substr(0, pos);
        std::string filename = path.substr(pos + 1);
        
        if (filename.find(".m3u8") != std::string::npos) {
            return server->HandleM3U8Request(session, streamPath);
        } else if (filename.find(".ts") != std::string::npos) {
            return server->HandleTSRequest(session, streamPath, path);
        }
    }
    
    http_server_set_status_code(session, 404, NULL);
    http_server_send(session, "", 0, NULL, NULL);
    return 0;
}

int HlsServer::HandleM3U8Request(http_session_t* session, const std::string& path) {
    auto playlist = GetOrCreatePlaylist(path);
    if (!playlist) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    std::string m3u8 = playlist->GetM3U8();
    if (m3u8.empty()) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    http_server_set_header(session, "Content-Type", "application/vnd.apple.mpegurl");
    http_server_set_header(session, "Cache-Control", "no-cache");
    http_server_send(session, m3u8.c_str(), m3u8.length(), NULL, NULL);
    return 0;
}

int HlsServer::HandleTSRequest(http_session_t* session, const std::string& path, const std::string& filename) {
    auto playlist = GetOrCreatePlaylist(path);
    if (!playlist) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    auto segment = playlist->GetSegment(filename);
    if (!segment) {
        http_server_set_status_code(session, 404, NULL);
        http_server_send(session, "", 0, NULL, NULL);
        return 0;
    }
    
    http_server_set_header(session, "Content-Type", "video/mp2t");
    http_server_send(session, segment->data.data(), segment->data.size(), 
                     [](void* param, int code, size_t bytes) {
                         auto seg = (HlsSegment*)param;
                         seg->refcount--;
                         return 0;
                     }, segment.get());
    return 0;
}

std::shared_ptr<HlsPlaylist> HlsServer::GetOrCreatePlaylist(const std::string& path) {
    std::lock_guard<std::mutex> lock(m_playlistMutex);
    
    auto it = m_playlists.find(path);
    if (it != m_playlists.end()) {
        return it->second;
    }
    
    auto playlist = std::make_shared<HlsPlaylist>(path, m_sourceManager);
    playlist->Start();
    m_playlists[path] = playlist;
    
    return playlist;
}