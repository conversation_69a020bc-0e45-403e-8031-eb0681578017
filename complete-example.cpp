#include <iostream>
#include <thread>
#include <chrono>
#include <signal.h>
#include <vector>
#include "recording-manager.h"

static bool g_running = true;

void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        g_running = false;
        std::cout << "\nReceived signal, stopping..." << std::endl;
    }
}

// 创建海康威视设备录像任务
RecordingTask CreateHikvisionTask() {
    RecordingTask task;
    
    // 基本信息
    task.task_id = "hikvision_camera_001";
    task.device_id = "HIK_001";
    task.sdk_type = SecurityChipSDKFactory::HIKVISION;
    
    // 设备连接配置
    task.chip_config.device_id = "HIK_001";
    task.chip_config.ip_address = "*************";
    task.chip_config.port = 8000;
    task.chip_config.username = "admin";
    task.chip_config.password = "123456";
    task.chip_config.video_channel = 0;
    task.chip_config.video_stream = 0; // 主码流
    task.chip_config.enable_audio = true;
    
    // 视频参数
    task.chip_config.video_width = 1920;
    task.chip_config.video_height = 1080;
    task.chip_config.video_fps = 25;
    task.chip_config.video_bitrate = 4096;
    
    // 音频参数
    task.chip_config.audio_sample_rate = 48000;
    task.chip_config.audio_channels = 2;
    task.chip_config.audio_bits_per_sample = 16;
    
    // 录像配置
    task.recording_config.output_dir = "./recordings/hikvision";
    task.recording_config.max_file_size = 200 * 1024 * 1024; // 200MB
    task.recording_config.max_duration = 600; // 10分钟
    task.recording_config.enable_hash_tagging = true;
    task.recording_config.file_prefix = "hik_cam001";
    
    // 录像策略
    task.continuous_recording = true;
    task.motion_detection = false;
    task.alarm_trigger = false;
    
    // 添加标签
    task.tags["location"] = "Building_A_Entrance";
    task.tags["camera_type"] = "Dome";
    task.tags["resolution"] = "1080P";
    task.tags["vendor"] = "Hikvision";
    task.tags["department"] = "Security";
    
    return task;
}

// 创建大华设备录像任务
RecordingTask CreateDahuaTask() {
    RecordingTask task;
    
    task.task_id = "dahua_camera_002";
    task.device_id = "DH_002";
    task.sdk_type = SecurityChipSDKFactory::DAHUA;
    
    task.chip_config.device_id = "DH_002";
    task.chip_config.ip_address = "*************";
    task.chip_config.port = 37777;
    task.chip_config.username = "admin";
    task.chip_config.password = "admin123";
    task.chip_config.video_channel = 0;
    task.chip_config.enable_audio = true;
    
    task.chip_config.video_width = 2560;
    task.chip_config.video_height = 1440;
    task.chip_config.video_fps = 30;
    task.chip_config.video_bitrate = 6144;
    
    task.recording_config.output_dir = "./recordings/dahua";
    task.recording_config.max_file_size = 300 * 1024 * 1024; // 300MB
    task.recording_config.max_duration = 900; // 15分钟
    task.recording_config.enable_hash_tagging = true;
    task.recording_config.file_prefix = "dh_cam002";
    
    task.continuous_recording = true;
    task.motion_detection = true; // 启用移动侦测
    task.pre_record_seconds = 10;
    task.post_record_seconds = 20;
    
    task.tags["location"] = "Building_B_Parking";
    task.tags["camera_type"] = "Bullet";
    task.tags["resolution"] = "1440P";
    task.tags["vendor"] = "Dahua";
    task.tags["zone"] = "Parking_Lot";
    
    return task;
}

// 演示录像文件管理功能
void DemoFileManagement(RecordingManager& manager) {
    std::cout << "\n=== File Management Demo ===" << std::endl;
    
    // 获取所有录像
    auto all_recordings = manager.GetAllRecordings();
    std::cout << "Total recordings: " << all_recordings.size() << std::endl;
    
    // 按设备查找录像
    auto hik_recordings = manager.GetRecordingsByDevice("HIK_001");
    std::cout << "Hikvision recordings: " << hik_recordings.size() << std::endl;
    
    // 按时间范围查找录像
    auto now = std::chrono::system_clock::now();
    auto one_hour_ago = now - std::chrono::hours(1);
    auto recent_recordings = manager.GetRecordingsByTimeRange(one_hour_ago, now);
    std::cout << "Recent recordings (last hour): " << recent_recordings.size() << std::endl;
    
    // 按标签搜索录像
    auto entrance_recordings = manager.SearchRecordings("Entrance");
    std::cout << "Entrance recordings: " << entrance_recordings.size() << std::endl;
    
    // 显示录像详情
    for (const auto& recording : recent_recordings) {
        std::cout << "  File: " << recording.filename << std::endl;
        std::cout << "    Size: " << recording.file_size / (1024*1024) << " MB" << std::endl;
        std::cout << "    Duration: " << recording.duration << " seconds" << std::endl;
        std::cout << "    MD5: " << recording.hash_md5.substr(0, 8) << "..." << std::endl;
        std::cout << "    Tags: ";
        for (const auto& tag : recording.tags) {
            std::cout << tag.first << "=" << tag.second << " ";
        }
        std::cout << std::endl << std::endl;
    }
}

// 演示存储管理功能
void DemoStorageManagement(RecordingManager& manager) {
    std::cout << "\n=== Storage Management Demo ===" << std::endl;
    
    uint64_t total_used = manager.GetTotalStorageUsed();
    uint64_t available = manager.GetAvailableStorage();
    
    std::cout << "Storage used: " << total_used / (1024*1024) << " MB" << std::endl;
    std::cout << "Storage available: " << available / (1024*1024) << " MB" << std::endl;
    
    double usage_percent = (double)total_used / (total_used + available) * 100.0;
    std::cout << "Storage usage: " << std::fixed << std::setprecision(1) << usage_percent << "%" << std::endl;
    
    // 演示清理功能
    if (usage_percent > 50.0) {
        std::cout << "Storage usage high, cleaning up old recordings..." << std::endl;
        bool cleaned = manager.CleanupOldRecordings(7); // 保留7天
        if (cleaned) {
            std::cout << "Old recordings cleaned up successfully" << std::endl;
        }
    }
}

int main() {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    std::cout << "=== Complete MP4 Recorder Example ===" << std::endl;
    std::cout << "This example demonstrates a complete recording system" << std::endl;
    std::cout << "with multiple camera support and advanced features." << std::endl;
    
    // 创建录像管理器
    RecordingManager manager;
    
    // 设置事件回调
    manager.SetTaskStateChangeCallback([](const std::string& task_id, TaskState old_state, TaskState new_state) {
        std::cout << "[" << task_id << "] State: " << static_cast<int>(old_state) 
                  << " -> " << static_cast<int>(new_state) << std::endl;
    });
    
    manager.SetRecordingCompleteCallback([](const std::string& task_id, const RecordingInfo& info) {
        std::cout << "[" << task_id << "] Recording completed: " << info.filename 
                  << " (" << info.file_size / (1024*1024) << "MB, " << info.duration << "s)" << std::endl;
    });
    
    manager.SetErrorCallback([](const std::string& task_id, const std::string& error) {
        std::cerr << "[" << task_id << "] Error: " << error << std::endl;
    });
    
    manager.SetStorageWarningCallback([](uint64_t used, uint64_t total) {
        double percent = (double)used / total * 100.0;
        std::cout << "Storage warning: " << std::fixed << std::setprecision(1) 
                  << percent << "% used" << std::endl;
    });
    
    // 启动管理器
    if (!manager.Start()) {
        std::cerr << "Failed to start recording manager" << std::endl;
        return -1;
    }
    
    // 创建录像任务
    std::vector<RecordingTask> tasks = {
        CreateHikvisionTask(),
        CreateDahuaTask()
    };
    
    // 添加任务
    for (const auto& task : tasks) {
        if (manager.AddTask(task)) {
            std::cout << "Added task: " << task.task_id << " (" << task.device_id << ")" << std::endl;
        } else {
            std::cerr << "Failed to add task: " << task.task_id << std::endl;
        }
    }
    
    // 启动所有任务
    std::cout << "\nStarting all recording tasks..." << std::endl;
    manager.StartAllTasks();
    
    // 主循环
    auto last_demo_time = std::chrono::steady_clock::now();
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        // 每60秒演示一次功能
        auto now = std::chrono::steady_clock::now();
        if (now - last_demo_time >= std::chrono::seconds(60)) {
            auto stats = manager.GetStatistics();
            std::cout << "\n=== Current Statistics ===" << std::endl;
            std::cout << "Running tasks: " << stats.running_tasks << "/" << stats.total_tasks << std::endl;
            std::cout << "Total recordings: " << stats.total_recordings << std::endl;
            std::cout << "Storage used: " << stats.total_storage_used / (1024*1024) << " MB" << std::endl;
            
            DemoFileManagement(manager);
            DemoStorageManagement(manager);
            
            last_demo_time = now;
        }
    }
    
    // 停止所有任务
    std::cout << "\nStopping all tasks..." << std::endl;
    manager.StopAllTasks();
    
    // 保存配置
    manager.SaveConfiguration("./config/final_config.json");
    
    // 停止管理器
    manager.Stop();
    
    // 最终统计
    auto final_stats = manager.GetStatistics();
    std::cout << "\n=== Final Statistics ===" << std::endl;
    std::cout << "Total recordings created: " << final_stats.total_recordings << std::endl;
    std::cout << "Total storage used: " << final_stats.total_storage_used / (1024*1024) << " MB" << std::endl;
    
    std::cout << "\nMP4 Recorder Example completed successfully!" << std::endl;
    return 0;
}
