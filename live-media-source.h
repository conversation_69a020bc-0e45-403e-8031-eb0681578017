#ifndef LIVE_MEDIA_SOURCE_H
#define LIVE_MEDIA_SOURCE_H

#include "MediaSourceManager.h"
#include <list>
#include <chrono>

class LiveMediaSource : public MediaSource {
public:
    LiveMediaSource(const std::string& path);
    ~LiveMediaSource();
    
    // MediaSource接口
    const std::string& GetStreamPath() const override { return m_path; }
    const MediaInfo& GetMediaInfo() const override { return m_mediaInfo; }
    bool IsLive() const override { return true; }
    
    void AddSink(std::weak_ptr<MediaSession> session) override;
    void RemoveSink(std::weak_ptr<MediaSession> session) override;
    void PushPacket(const MediaPacket& packet) override;
    
    // 设置媒体信息
    void SetMediaInfo(const MediaInfo& info);
    
private:
    std::string m_path;
    MediaInfo m_mediaInfo;
    mutable std::mutex m_mutex;
    
    // 会话管理
    std::list<std::weak_ptr<MediaSession>> m_sessions;
    
    // GOP缓存（关键帧组）
    std::list<MediaPacket> m_gopCache;
    bool m_hasKeyframe = false;
    
    // 统计信息
    uint64_t m_packetCount = 0;
    uint64_t m_byteCount = 0;
    std::chrono::steady_clock::time_point m_startTime;
    
    void CleanupSessions();
    void UpdateGOPCache(const MediaPacket& packet);
    void SendGOPCache(std::weak_ptr<MediaSession> session);
};

#endif // LIVE_MEDIA_SOURCE_H