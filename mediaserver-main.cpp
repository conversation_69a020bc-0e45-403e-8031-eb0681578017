#include <iostream>
#include <thread>
#include <memory>
#include <signal.h>
#include "MediaServer.h"
#include "RtmpServer.h"
#include "RtspServer.h"
#include "HlsServer.h"
#include "HttpFlvServer.h"
#include "MediaSourceManager.h"

static bool g_running = true;

void signal_handler(int sig) {
    if (sig == SIGINT || sig == SIGTERM) {
        g_running = false;
    }
}

int main(int argc, char* argv[]) {
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);

    // 初始化媒体源管理器
    auto sourceManager = std::make_shared<MediaSourceManager>();
    
    // 创建媒体服务器
    MediaServer server(sourceManager);
    
    // 配置服务器
    MediaServerConfig config;
    config.rtmp_port = 1935;
    config.rtsp_port = 554;
    config.http_port = 8080;
    config.worker_threads = std::thread::hardware_concurrency();
    
    // 启动服务器
    if (!server.Start(config)) {
        std::cerr << "Failed to start media server" << std::endl;
        return -1;
    }
    
    std::cout << "Media Server started successfully" << std::endl;
    std::cout << "RTMP Port: " << config.rtmp_port << std::endl;
    std::cout << "RTSP Port: " << config.rtsp_port << std::endl;
    std::cout << "HTTP Port: " << config.http_port << std::endl;
    
    // 主循环
    while (g_running) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    
    // 停止服务器
    server.Stop();
    
    std::cout << "Media Server stopped" << std::endl;
    return 0;
}