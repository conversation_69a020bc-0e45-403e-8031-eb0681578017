#include "RtmpServer.h"
#include "flv-proto.h"
#include "flv-muxer.h"
#include <iostream>
#include <cstring>

RtmpServer::RtmpServer(std::shared_ptr<MediaSourceManager> sourceManager)
    : m_sourceManager(sourceManager) {
    socket_init();
}

RtmpServer::~RtmpServer() {
    Stop();
    socket_cleanup();
}

bool RtmpServer::Start(int port) {
    if (m_running) {
        return false;
    }
    
    m_socket = socket_tcp_listen_ipv4(NULL, port, SOMAXCONN);
    if (socket_invalid == m_socket) {
        std::cerr << "Failed to create RTMP listening socket on port " << port << std::endl;
        return false;
    }
    
    m_running = true;
    m_acceptThread = std::thread(&RtmpServer::AcceptThread, this);
    
    return true;
}

void RtmpServer::Stop() {
    if (!m_running) {
        return;
    }
    
    m_running = false;
    
    if (socket_invalid != m_socket) {
        socket_close(m_socket);
        m_socket = socket_invalid;
    }
    
    if (m_acceptThread.joinable()) {
        m_acceptThread.join();
    }
    
    // 停止所有会话
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        for (auto& pair : m_sessions) {
            pair.second->Stop();
        }
        m_sessions.clear();
    }
}

void RtmpServer::AcceptThread() {
    while (m_running) {
        struct sockaddr_storage addr;
        socklen_t addrlen = sizeof(addr);
        
        socket_t client = socket_accept(m_socket, &addr, &addrlen);
        if (socket_invalid == client) {
            continue;
        }
        
        // 创建新线程处理客户端
        std::thread clientThread(&RtmpServer::HandleClient, this, client);
        clientThread.detach();
    }
}

void RtmpServer::HandleClient(socket_t client) {
    // 创建RTMP服务器处理器
    struct rtmp_server_handler_t handler;
    memset(&handler, 0, sizeof(handler));
    handler.send = OnSend;
    handler.onplay = OnPlay;
    handler.onpublish = OnPublish;
    handler.onpause = OnPause;
    handler.onseek = OnSeek;
    handler.onvideo = OnVideo;
    handler.onaudio = OnAudio;
    handler.onscript = OnScript;
    handler.ongetduration = OnGetDuration;
    
    rtmp_server_t* rtmp = rtmp_server_create(this, &handler);
    if (!rtmp) {
        socket_close(client);
        return;
    }
    
    // 创建会话
    auto session = std::make_shared<RtmpSession>(client, rtmp, m_sourceManager);
    
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_sessions[session.get()] = session;
    }
    
    // 启动会话
    session->Start();
    
    // 会话结束后清理
    {
        std::lock_guard<std::mutex> lock(m_sessionMutex);
        m_sessions.erase(session.get());
    }
    
    rtmp_server_destroy(rtmp);
}

// RTMP回调实现
int RtmpServer::OnSend(void* param, const void* header, size_t len, const void* data, size_t bytes) {
    RtmpServer* server = (RtmpServer*)param;
    // 查找对应的会话
    // 这里需要通过rtmp_server_t找到对应的RtmpSession
    // 暂时简化处理
    return 0;
}

int RtmpServer::OnPlay(void* param, const char* app, const char* stream, double start, double duration, uint8_t reset) {
    RtmpServer* server = (RtmpServer*)param;
    // TODO: 实现播放逻辑
    return 0;
}

int RtmpServer::OnPublish(void* param, const char* app, const char* stream, const char* type) {
    RtmpServer* server = (RtmpServer*)param;
    // TODO: 实现发布逻辑
    return 0;
}

int RtmpServer::OnPause(void* param, int pause, uint32_t ms) {
    return 0;
}

int RtmpServer::OnSeek(void* param, uint32_t ms) {
    return 0;
}

int RtmpServer::OnVideo(void* param, const void* data, size_t bytes, uint32_t timestamp) {
    // TODO: 处理视频数据
    return 0;
}

int RtmpServer::OnAudio(void* param, const void* data, size_t bytes, uint32_t timestamp) {
    // TODO: 处理音频数据
    return 0;
}

int RtmpServer::OnScript(void* param, const void* data, size_t bytes, uint32_t timestamp) {
    // TODO: 处理脚本数据
    return 0;
}

int RtmpServer::OnGetDuration(void* param, const char* app, const char* stream, double* duration) {
    *duration = 0;
    return 0;
}

// RtmpSession实现
RtmpSession::RtmpSession(socket_t socket, rtmp_server_t* rtmp, std::shared_ptr<MediaSourceManager> sourceManager)
    : m_socket(socket), m_rtmp(rtmp), m_sourceManager(sourceManager) {
}

RtmpSession::~RtmpSession() {
    Stop();
}

void RtmpSession::Start() {
    m_running = true;
    m_thread = std::thread(&RtmpSession::NetworkThread, this);
}

void RtmpSession::Stop() {
    m_running = false;
    
    if (m_socket != socket_invalid) {
        socket_close(m_socket);
        m_socket = socket_invalid;
    }
    
    if (m_thread.joinable()) {
        m_thread.join();
    }
    
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
    }
}

void RtmpSession::NetworkThread() {
    uint8_t buffer[4096];
    
    while (m_running) {
        int r = socket_recv(m_socket, buffer, sizeof(buffer), 0);
        if (r <= 0) {
            break;
        }
        
        size_t bytes = r;
        if (0 != rtmp_server_input(m_rtmp, buffer, &bytes)) {
            break;
        }
    }
    
    m_running = false;
}

int RtmpSession::Send(const void* header, size_t len, const void* data, size_t bytes) {
    if (!m_running || m_socket == socket_invalid) {
        return -1;
    }
    
    socket_bufvec_t vec[2];
    socket_setbufvec(vec, 0, (void*)header, len);
    socket_setbufvec(vec, 1, (void*)data, bytes);
    
    return socket_send_v_all_by_time(m_socket, vec, bytes ? 2 : 1, 0, 2000);
}

void RtmpSession::SetMediaSource(std::shared_ptr<MediaSource> source) {
    if (m_mediaSource) {
        m_mediaSource->RemoveSink(weak_from_this());
    }
    
    m_mediaSource = source;
    
    if (m_mediaSource) {
        m_mediaSource->AddSink(weak_from_this());
        
        // 获取媒体信息
        m_mediaInfo = m_mediaSource->GetMediaInfo();
        
        // 发送元数据
        SendMetadata();
        
        // 发送配置信息
        if (m_mediaInfo.has_video) {
            SendVideoConfig();
        }
        if (m_mediaInfo.has_audio) {
            SendAudioConfig();
        }
    }
}

void RtmpSession::OnMediaPacket(const MediaPacket& packet) {
    if (!m_running || !m_rtmp) {
        return;
    }
    
    // 根据包类型发送数据
    switch (packet.type) {
    case MediaPacket::VIDEO_H264:
    case MediaPacket::VIDEO_H265:
        rtmp_server_send_video(m_rtmp, packet.data.data(), packet.data.size(), packet.timestamp);
        break;
        
    case MediaPacket::AUDIO_AAC:
    case MediaPacket::AUDIO_G711A:
    case MediaPacket::AUDIO_G711U:
        rtmp_server_send_audio(m_rtmp, packet.data.data(), packet.data.size(), packet.timestamp);
        break;
        
    default:
        break;
    }
}

void RtmpSession::OnMediaInfo(const MediaInfo& info) {
    m_mediaInfo = info;
    SendMetadata();
}

void RtmpSession::SendMetadata() {
    // TODO: 实现元数据发送
}

void RtmpSession::SendVideoConfig() {
    // TODO: 实现视频配置发送
}

void RtmpSession::SendAudioConfig() {
    // TODO: 实现音频配置发送
}