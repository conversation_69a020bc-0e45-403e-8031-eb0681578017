# MediaServer Makefile

# 编译器设置
CXX = g++
CXXFLAGS = -std=c++11 -Wall -g -O2
LDFLAGS = -pthread

# 目录设置
SRC_DIR = .
INC_DIR = .
OBJ_DIR = obj
BIN_DIR = bin

# 依赖的media-server路径
MEDIA_SERVER_DIR = ../media-server
SDK_DIR = ../sdk

# 包含路径
INCLUDES = -I$(INC_DIR) \
           -I$(MEDIA_SERVER_DIR)/librtmp/include \
           -I$(MEDIA_SERVER_DIR)/librtsp/include \
           -I$(MEDIA_SERVER_DIR)/libhls/include \
           -I$(MEDIA_SERVER_DIR)/libflv/include \
           -I$(MEDIA_SERVER_DIR)/librtp/include \
           -I$(MEDIA_SERVER_DIR)/libmpeg/include \
           -I$(SDK_DIR)/include \
           -I$(SDK_DIR)/libaio/include \
           -I$(SDK_DIR)/libhttp/include

# 库路径
LIBPATH = -L$(MEDIA_SERVER_DIR)/librtmp \
          -L$(MEDIA_SERVER_DIR)/librtsp \
          -L$(MEDIA_SERVER_DIR)/libhls \
          -L$(MEDIA_SERVER_DIR)/libflv \
          -L$(MEDIA_SERVER_DIR)/librtp \
          -L$(MEDIA_SERVER_DIR)/libmpeg \
          -L$(SDK_DIR)/libaio \
          -L$(SDK_DIR)/libhttp

# 链接的库
LIBS = -lrtmp -lrtsp -lhls -lflv -lrtp -lmpeg -laio -lhttp -lpthread -lrt

# 源文件
SRCS = $(wildcard $(SRC_DIR)/*.cpp)
OBJS = $(patsubst $(SRC_DIR)/%.cpp,$(OBJ_DIR)/%.o,$(SRCS))

# 目标文件
TARGET = $(BIN_DIR)/mediaserver

# 默认目标
all: directories $(TARGET)

# 创建目录
directories:
	@mkdir -p $(OBJ_DIR)
	@mkdir -p $(BIN_DIR)

# 编译目标
$(TARGET): $(OBJS)
	$(CXX) $(LDFLAGS) -o $@ $^ $(LIBPATH) $(LIBS)

# 编译规则
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.cpp
	$(CXX) $(CXXFLAGS) $(INCLUDES) -c $< -o $@

# 清理
clean:
	rm -rf $(OBJ_DIR) $(BIN_DIR)

# 安装
install: all
	@echo "Installing mediaserver..."
	@cp $(TARGET) /usr/local/bin/

# 运行
run: all
	$(TARGET)

# 依赖关系
depend:
	$(CXX) $(INCLUDES) -MM $(SRCS) > .depend

-include .depend

.PHONY: all clean install run depend directories