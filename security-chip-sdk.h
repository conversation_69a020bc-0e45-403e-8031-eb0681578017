#ifndef SECURITY_CHIP_SDK_H
#define SECURITY_CHIP_SDK_H

#include <string>
#include <vector>
#include <functional>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <queue>
#include "mp4-recorder.h"

// 安防芯片配置
struct SecurityChipConfig {
    std::string device_id;              // 设备ID
    std::string ip_address = "*************";  // 设备IP地址
    int port = 8000;                    // 设备端口
    std::string username = "admin";     // 用户名
    std::string password = "123456";    // 密码
    
    // 视频流配置
    int video_channel = 0;              // 视频通道
    int video_stream = 0;               // 视频流（主码流/子码流）
    int video_width = 1920;             // 视频宽度
    int video_height = 1080;            // 视频高度
    int video_fps = 25;                 // 帧率
    int video_bitrate = 2048;           // 码率(kbps)
    
    // 音频流配置
    bool enable_audio = true;           // 启用音频
    int audio_channel = 0;              // 音频通道
    int audio_sample_rate = 48000;      // 采样率
    int audio_channels = 2;             // 声道数
    int audio_bits_per_sample = 16;     // 位深
};

// 流数据回调类型
using StreamDataCallback = std::function<void(const MediaFrame& frame)>;
using ConnectionStatusCallback = std::function<void(bool connected, const std::string& error)>;

// 安防芯片SDK接口类
class SecurityChipSDK {
public:
    SecurityChipSDK(const SecurityChipConfig& config);
    virtual ~SecurityChipSDK();
    
    // 连接和断开
    virtual bool Connect();
    virtual bool Disconnect();
    virtual bool IsConnected() const { return m_connected; }
    
    // 开始/停止流
    virtual bool StartStream();
    virtual bool StopStream();
    virtual bool IsStreaming() const { return m_streaming; }
    
    // 设置回调
    void SetStreamDataCallback(StreamDataCallback callback) { m_stream_callback = callback; }
    void SetConnectionStatusCallback(ConnectionStatusCallback callback) { m_status_callback = callback; }
    
    // 获取流参数
    virtual bool GetVideoParams(int& width, int& height, int& fps, 
                               std::vector<uint8_t>& sps, std::vector<uint8_t>& pps);
    virtual bool GetAudioParams(int& channels, int& sample_rate, int& bits_per_sample,
                               std::vector<uint8_t>& config);
    
    // 设备控制
    virtual bool SetVideoParams(int width, int height, int fps, int bitrate);
    virtual bool SetAudioParams(int sample_rate, int channels, int bits_per_sample);
    
    // 获取设备信息
    virtual std::string GetDeviceInfo() const;
    virtual std::string GetFirmwareVersion() const;
    
protected:
    SecurityChipConfig m_config;
    std::atomic<bool> m_connected{false};
    std::atomic<bool> m_streaming{false};
    
    // 回调函数
    StreamDataCallback m_stream_callback;
    ConnectionStatusCallback m_status_callback;
    
    // 线程管理
    std::thread m_receive_thread;
    std::atomic<bool> m_running{false};
    
    // 数据缓冲
    std::mutex m_buffer_mutex;
    std::queue<std::vector<uint8_t>> m_video_buffer;
    std::queue<std::vector<uint8_t>> m_audio_buffer;
    
    // 流参数
    std::vector<uint8_t> m_video_sps;
    std::vector<uint8_t> m_video_pps;
    std::vector<uint8_t> m_audio_config;
    
    // 虚拟方法，由具体厂商SDK实现
    virtual bool ConnectDevice() = 0;
    virtual bool DisconnectDevice() = 0;
    virtual bool StartVideoStream() = 0;
    virtual bool StartAudioStream() = 0;
    virtual bool StopVideoStream() = 0;
    virtual bool StopAudioStream() = 0;
    
    // 数据接收线程
    virtual void ReceiveThread();
    
    // 数据处理
    virtual void ProcessVideoData(const uint8_t* data, size_t size, int64_t timestamp);
    virtual void ProcessAudioData(const uint8_t* data, size_t size, int64_t timestamp);
    
    // H.264 NALU解析
    bool ParseH264NALU(const uint8_t* data, size_t size, std::vector<MediaFrame>& frames);
    bool ParseSPS(const uint8_t* data, size_t size);
    bool ParsePPS(const uint8_t* data, size_t size);
    
    // AAC帧解析
    bool ParseAACFrame(const uint8_t* data, size_t size, MediaFrame& frame);
};

// 海康威视SDK实现
class HikvisionSDK : public SecurityChipSDK {
public:
    HikvisionSDK(const SecurityChipConfig& config);
    ~HikvisionSDK();
    
protected:
    bool ConnectDevice() override;
    bool DisconnectDevice() override;
    bool StartVideoStream() override;
    bool StartAudioStream() override;
    bool StopVideoStream() override;
    bool StopAudioStream() override;
    
private:
    void* m_login_handle;       // 登录句柄
    void* m_video_handle;       // 视频句柄
    void* m_audio_handle;       // 音频句柄
    
    // 海康SDK回调函数
    static void VideoDataCallback(int lRealHandle, unsigned int dwDataType, 
                                 unsigned char* pBuffer, unsigned int dwBufSize, void* pUser);
    static void AudioDataCallback(int lRealHandle, unsigned int dwDataType,
                                 unsigned char* pBuffer, unsigned int dwBufSize, void* pUser);
};

// 大华SDK实现
class DahuaSDK : public SecurityChipSDK {
public:
    DahuaSDK(const SecurityChipConfig& config);
    ~DahuaSDK();
    
protected:
    bool ConnectDevice() override;
    bool DisconnectDevice() override;
    bool StartVideoStream() override;
    bool StartAudioStream() override;
    bool StopVideoStream() override;
    bool StopAudioStream() override;
    
private:
    void* m_login_handle;
    void* m_video_handle;
    void* m_audio_handle;
    
    // 大华SDK回调函数
    static int VideoDataCallback(int lRealHandle, int dwDataType,
                                unsigned char* pBuffer, int dwBufSize, void* dwUser);
    static int AudioDataCallback(int lRealHandle, int dwDataType,
                                unsigned char* pBuffer, int dwBufSize, void* dwUser);
};

// 宇视SDK实现
class UnivviewSDK : public SecurityChipSDK {
public:
    UnivviewSDK(const SecurityChipConfig& config);
    ~UnivviewSDK();
    
protected:
    bool ConnectDevice() override;
    bool DisconnectDevice() override;
    bool StartVideoStream() override;
    bool StartAudioStream() override;
    bool StopVideoStream() override;
    bool StopAudioStream() override;
    
private:
    void* m_session_handle;
    void* m_stream_handle;
    
    // 宇视SDK回调函数
    static void StreamDataCallback(void* handle, void* pUser, void* pData);
};

// SDK工厂类
class SecurityChipSDKFactory {
public:
    enum SDKType {
        HIKVISION,
        DAHUA,
        UNIVIEW,
        GENERIC
    };
    
    static std::unique_ptr<SecurityChipSDK> CreateSDK(SDKType type, const SecurityChipConfig& config);
    static SDKType DetectSDKType(const std::string& device_info);
};

#endif // SECURITY_CHIP_SDK_H
