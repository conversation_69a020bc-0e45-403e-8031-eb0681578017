﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hh;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav;mfcribbon-ms</Extensions>
    </Filter>
    <Filter Include="librtmp">
      <UniqueIdentifier>{f3db94fb-de80-40f1-b2f1-1adc88726e92}</UniqueIdentifier>
    </Filter>
    <Filter Include="libflv">
      <UniqueIdentifier>{83bd3232-d071-494c-a892-2bb471e72e98}</UniqueIdentifier>
    </Filter>
    <Filter Include="libmov">
      <UniqueIdentifier>{753cb105-02dc-435b-86c5-71de6fc82b84}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtmp\aio">
      <UniqueIdentifier>{75338d24-c7bc-47e9-b2d3-0a155b919f0b}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtsp">
      <UniqueIdentifier>{a32f7f42-2c3d-44f5-9876-73289ca5ac43}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtsp\media">
      <UniqueIdentifier>{b1c91a94-0572-4ed6-bfb1-acd36ab71e40}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtp">
      <UniqueIdentifier>{6ccbd831-80b9-47aa-bbb2-208bd9f20c33}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtsp\aio">
      <UniqueIdentifier>{84a6ad98-af6f-4aa9-bbbd-31d247b1c0ec}</UniqueIdentifier>
    </Filter>
    <Filter Include="libmpeg">
      <UniqueIdentifier>{4409985a-d2a4-4ed1-85d4-99c3d778a759}</UniqueIdentifier>
    </Filter>
    <Filter Include="libhls">
      <UniqueIdentifier>{54c09e9c-2702-42c9-9ced-e11f70c07890}</UniqueIdentifier>
    </Filter>
    <Filter Include="libdash">
      <UniqueIdentifier>{042f0745-3d50-44cf-948d-44e13d0f0d41}</UniqueIdentifier>
    </Filter>
    <Filter Include="libhttp">
      <UniqueIdentifier>{8de6b9a5-f676-40e5-b5b7-72587b84f844}</UniqueIdentifier>
    </Filter>
    <Filter Include="libsip">
      <UniqueIdentifier>{47092231-3039-49a4-8cc2-83f4ed15d45b}</UniqueIdentifier>
    </Filter>
    <Filter Include="librtsp\sdp">
      <UniqueIdentifier>{65f4c8fc-27c7-4b37-9c1b-de6acfc134bc}</UniqueIdentifier>
    </Filter>
    <Filter Include="libmkv">
      <UniqueIdentifier>{6ad79cfa-2fb8-4e75-b04b-48ad86d6aaa7}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="..\librtmp\aio\aio-rtmp-client.h">
      <Filter>librtmp\aio</Filter>
    </ClInclude>
    <ClInclude Include="..\librtmp\aio\aio-rtmp-server.h">
      <Filter>librtmp\aio</Filter>
    </ClInclude>
    <ClInclude Include="..\librtmp\aio\aio-rtmp-transport.h">
      <Filter>librtmp\aio</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\rtp-udp-transport.h">
      <Filter>librtsp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\rtp-tcp-transport.h">
      <Filter>librtsp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\rtsp-vod.h">
      <Filter>librtsp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\mp4-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\avpacket-queue.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\ffmpeg-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\ffmpeg-live-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\h264-file-reader.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\h264-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\media-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\pcm-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\ps-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\vod-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\mp4-file-reader.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtp\test\rtp-dump.h">
      <Filter>librtp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\source\utils\rtp-sender.h">
      <Filter>librtsp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\source\utils\rtsp-payloads.h">
      <Filter>librtsp</Filter>
    </ClInclude>
    <ClInclude Include="..\libmov\test\mov-file-buffer.h">
      <Filter>libmov</Filter>
    </ClInclude>
    <ClInclude Include="..\librtmp\test\RTMPUrl.h">
      <Filter>librtmp</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\h265-file-reader.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\h265-file-source.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
    <ClInclude Include="..\librtsp\test\media\ps-file-reader.h">
      <Filter>librtsp\media</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="test.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\amf0-test.c">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\flv2ts-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\flv-reader-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\flv-read-write-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\h264-flv-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\ts2flv-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-play-aio-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-play-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-publish-aio-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-publish-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-publish-aio-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-publish-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-vod-aio-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-vod-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\fmp4-writer-test.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-2-flv.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-reader-test.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-audio.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-h264.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-h265.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-test.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\aio\aio-rtmp-client.c">
      <Filter>librtmp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\aio\aio-rtmp-server.c">
      <Filter>librtmp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\aio\aio-rtmp-transport.c">
      <Filter>librtmp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-client-test.c">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-payload-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-receiver-test.c">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\server\aio\rtsp-server-listen.c">
      <Filter>librtsp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\server\aio\rtsp-server-tcp.c">
      <Filter>librtsp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\server\aio\rtsp-server-udp.c">
      <Filter>librtsp\aio</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ts-dec-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ts-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="BinaryDiff.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="..\libhls\demo\hls-segmenter-flv.cpp">
      <Filter>libhls</Filter>
    </ClCompile>
    <ClCompile Include="..\libhls\demo\hls-segmenter-mp4.cpp">
      <Filter>libhls</Filter>
    </ClCompile>
    <ClCompile Include="..\libhls\demo\hls-server.cpp">
      <Filter>libhls</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-list-dir.cpp">
      <Filter>libhls</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-file-buffer.c">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtp-udp-transport.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-forward-aio-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\libdash\test\dash-dynamic-test.cpp">
      <Filter>libdash</Filter>
    </ClCompile>
    <ClCompile Include="..\libdash\test\dash-static-test.cpp">
      <Filter>libdash</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\benchmark.c">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-client-test.cpp">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-client-test2.cpp">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-server-test.cpp">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-download.c">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libhttp\test\http-parser-test.c">
      <Filter>libhttp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ps-dec-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ps-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-aac.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-g7xx.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-h264.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-h265.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-opus.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-header-test.c">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-timer.c">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uac-message-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uac-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uas-message-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uas-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\transport-tcp.c">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\transport-udp.c">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-push-server.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\h265-flv-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\mp4-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-mpeg4.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-server-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\avpacket-queue.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\ffmpeg-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\ffmpeg-live-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\h264-file-reader.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\h264-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\pcm-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\ps-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\flv-2-mpeg-ps-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uas-test2.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-uac-test2.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-agent-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-chunk-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-input-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-input-test.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\vod-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\mp4-file-reader.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\..\sdk\libice\test\ice-transport.c">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\sdp-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\libsip\test\sip-message-test.cpp">
      <Filter>libsip</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-fmtp-load.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ts-encrypt-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\mov-rtp-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-dump.c">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-dump-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-sender-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\utils\rtp-sender.c">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\utils\rtsp-demuxer.c">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\utils\rtsp-muxer.c">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-mpeg2.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-vpx.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mkv-2-mp4-test.cpp">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mkv-file-buffer.c">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mkv-reader-test.cpp">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mkv-writer-test.cpp">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mkv-writer-test2.cpp">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\flv-parser-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\http-flv-live.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\fmp4-writer-test2.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-adts.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-subtitle.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ts-multi-program-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\librtmp\test\rtmp-server-publish-benchmark.cpp">
      <Filter>librtmp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-dump-replay.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\rtp-queue-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtp-streaming-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-demuxer-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\sdp-receiver-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-av1.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-payload.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmkv\test\mvk-writer-audio.cpp">
      <Filter>libmkv</Filter>
    </ClCompile>
    <ClCompile Include="..\libmov\test\mov-writer-av1.cpp">
      <Filter>libmov</Filter>
    </ClCompile>
    <ClCompile Include="..\librtp\test\av1-rtp-test.cpp">
      <Filter>librtp</Filter>
    </ClCompile>
    <ClCompile Include="..\libflv\test\av1-flv-test.cpp">
      <Filter>libflv</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-client-input-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\h265-file-reader.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\h265-file-source.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-client-push-test.cpp">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mpeg-ps-2-flv-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\rtsp-client-test2.c">
      <Filter>librtsp</Filter>
    </ClCompile>
    <ClCompile Include="..\libmpeg\test\mov-2-mpeg-ps-test.cpp">
      <Filter>libmpeg</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\source\sdp\sdp-h266.c">
      <Filter>librtsp\sdp</Filter>
    </ClCompile>
    <ClCompile Include="..\librtsp\test\media\ps-file-reader.cpp">
      <Filter>librtsp\media</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <None Include="..\libflv\test\rtmp.onStatus.amf0">
      <Filter>libflv</Filter>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Text Include="..\librtsp\test\sdp1.txt">
      <Filter>librtsp</Filter>
    </Text>
  </ItemGroup>
</Project>