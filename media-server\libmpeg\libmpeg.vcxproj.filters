﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <Filter Include="Source Files">
      <UniqueIdentifier>{4FC737F1-C7A5-4376-A066-2A32D752A2FF}</UniqueIdentifier>
      <Extensions>cpp;c;cc;cxx;def;odl;idl;hpj;bat;asm;asmx</Extensions>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{93995380-89BD-4b04-88EB-625FBE52EBFB}</UniqueIdentifier>
      <Extensions>h;hpp;hxx;hm;inl;inc;xsd</Extensions>
    </Filter>
    <Filter Include="Resource Files">
      <UniqueIdentifier>{67DA6AB6-F800-4c08-8B7A-83BB121AAD01}</UniqueIdentifier>
      <Extensions>rc;ico;cur;bmp;dlg;rc2;rct;bin;rgs;gif;jpg;jpeg;jpe;resx;tiff;tif;png;wav</Extensions>
    </Filter>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="source\mpeg-element-descriptor.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-pat.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-pes.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-pmt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ps-dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ps-enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-psd.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-psm.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ts-dec.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ts-enc.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-util.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ts-h264.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ts-h265.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-packet.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-pack-header.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-system-header.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-crc32.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-sdt.c">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="source\mpeg-ts-h266.c">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="include\mpeg-element-descriptor.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-ps.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-ts.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-types.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\mpeg-ts-opus.h">
      <Filter>Source Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-proto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-pes-proto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-ps-proto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-ts-proto.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\mpeg-pes-internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\mpeg-ps-internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="source\mpeg-ts-internal.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="include\mpeg-util.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
</Project>