// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 50;
	objects = {

/* Begin PBXBuildFile section */
		461F0CA6232B404400A995BD /* sip-subscribe.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CA5232B404400A995BD /* sip-subscribe.c */; };
		461F0CA8232B406400A995BD /* sip-header-substate.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CA7232B406400A995BD /* sip-header-substate.c */; };
		461F0CD5232B40AE00A995BD /* sip-uac.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CC7232B40AD00A995BD /* sip-uac.c */; };
		461F0CD6232B40AE00A995BD /* sip-uac-options.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CC8232B40AD00A995BD /* sip-uac-options.c */; };
		461F0CD7232B40AE00A995BD /* sip-uac-bye.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CC9232B40AD00A995BD /* sip-uac-bye.c */; };
		461F0CD8232B40AE00A995BD /* sip-uac-transaction-invite.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CCA232B40AD00A995BD /* sip-uac-transaction-invite.c */; };
		461F0CD9232B40AE00A995BD /* sip-uac-info.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CCB232B40AD00A995BD /* sip-uac-info.c */; };
		461F0CDB232B40AE00A995BD /* sip-uac-transaction.h in Headers */ = {isa = PBXBuildFile; fileRef = 461F0CCD232B40AD00A995BD /* sip-uac-transaction.h */; };
		461F0CDC232B40AE00A995BD /* sip-uac-transaction-noninvite.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CCE232B40AD00A995BD /* sip-uac-transaction-noninvite.c */; };
		461F0CDD232B40AE00A995BD /* sip-uac-subscribe.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CCF232B40AD00A995BD /* sip-uac-subscribe.c */; };
		461F0CDE232B40AE00A995BD /* sip-uac-transaction.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CD0232B40AD00A995BD /* sip-uac-transaction.c */; };
		461F0CDF232B40AE00A995BD /* sip-uac-cancel.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CD1232B40AE00A995BD /* sip-uac-cancel.c */; };
		461F0CE0232B40AE00A995BD /* sip-uac-register.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CD2232B40AE00A995BD /* sip-uac-register.c */; };
		461F0CE1232B40AE00A995BD /* sip-uac-invite.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CD3232B40AE00A995BD /* sip-uac-invite.c */; };
		461F0CE2232B40AE00A995BD /* sip-uac-ack.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CD4232B40AE00A995BD /* sip-uac-ack.c */; };
		461F0CF0232B40BF00A995BD /* sip-uas-register.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE3232B40BF00A995BD /* sip-uas-register.c */; };
		461F0CF1232B40BF00A995BD /* sip-uas-transaction-noninvite.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE4232B40BF00A995BD /* sip-uas-transaction-noninvite.c */; };
		461F0CF2232B40BF00A995BD /* sip-uas-transaction.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE5232B40BF00A995BD /* sip-uas-transaction.c */; };
		461F0CF3232B40BF00A995BD /* sip-uas-transaction-invite.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE6232B40BF00A995BD /* sip-uas-transaction-invite.c */; };
		461F0CF4232B40BF00A995BD /* sip-uas-prack.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE7232B40BF00A995BD /* sip-uas-prack.c */; };
		461F0CF5232B40BF00A995BD /* sip-uas-options.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CE8232B40BF00A995BD /* sip-uas-options.c */; };
		461F0CF6232B40BF00A995BD /* sip-uas-transaction.h in Headers */ = {isa = PBXBuildFile; fileRef = 461F0CE9232B40BF00A995BD /* sip-uas-transaction.h */; };
		461F0CF7232B40BF00A995BD /* sip-uas-cancel.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CEA232B40BF00A995BD /* sip-uas-cancel.c */; };
		461F0CF8232B40BF00A995BD /* sip-uas.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CEB232B40BF00A995BD /* sip-uas.c */; };
		461F0CF9232B40BF00A995BD /* sip-uas-subscribe.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CEC232B40BF00A995BD /* sip-uas-subscribe.c */; };
		461F0CFA232B40BF00A995BD /* sip-uas-bye.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CED232B40BF00A995BD /* sip-uas-bye.c */; };
		461F0CFB232B40BF00A995BD /* sip-uas-refer.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CEE232B40BF00A995BD /* sip-uas-refer.c */; };
		461F0CFC232B40BF00A995BD /* sip-uas-info.c in Sources */ = {isa = PBXBuildFile; fileRef = 461F0CEF232B40BF00A995BD /* sip-uas-info.c */; };
		461F0CFE232B40F600A995BD /* sip-internal.h in Headers */ = {isa = PBXBuildFile; fileRef = 461F0CFD232B40F600A995BD /* sip-internal.h */; };
		46A0D4F8229790E10070E1F5 /* sip-agent.c in Sources */ = {isa = PBXBuildFile; fileRef = 46A0D4F7229790E10070E1F5 /* sip-agent.c */; };
		46C5B4652183EE7000419E57 /* sip-reason.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B4472183EE7000419E57 /* sip-reason.c */; };
		46C5B4662183EE7000419E57 /* sip-message.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B4482183EE7000419E57 /* sip-message.c */; };
		46C5B4672183EE7000419E57 /* sip-dialog.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B4492183EE7000419E57 /* sip-dialog.c */; };
		46C5B4682183EE7000419E57 /* sip-header-contact.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B44B2183EE7000419E57 /* sip-header-contact.c */; };
		46C5B4692183EE7000419E57 /* sip-header-param.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B44C2183EE7000419E57 /* sip-header-param.c */; };
		46C5B46A2183EE7000419E57 /* sip-header-cseq.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B44D2183EE7000419E57 /* sip-header-cseq.c */; };
		46C5B46B2183EE7000419E57 /* sip-header-uri.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B44E2183EE7000419E57 /* sip-header-uri.c */; };
		46C5B46C2183EE7000419E57 /* sip-header-via.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B44F2183EE7000419E57 /* sip-header-via.c */; };
		46C5B46D2183EE7000419E57 /* sip-header.c in Sources */ = {isa = PBXBuildFile; fileRef = 46C5B4502183EE7000419E57 /* sip-header.c */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		461F0CA5232B404400A995BD /* sip-subscribe.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-subscribe.c"; sourceTree = "<group>"; };
		461F0CA7232B406400A995BD /* sip-header-substate.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-substate.c"; sourceTree = "<group>"; };
		461F0CC7232B40AD00A995BD /* sip-uac.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac.c"; sourceTree = "<group>"; };
		461F0CC8232B40AD00A995BD /* sip-uac-options.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-options.c"; sourceTree = "<group>"; };
		461F0CC9232B40AD00A995BD /* sip-uac-bye.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-bye.c"; sourceTree = "<group>"; };
		461F0CCA232B40AD00A995BD /* sip-uac-transaction-invite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-transaction-invite.c"; sourceTree = "<group>"; };
		461F0CCB232B40AD00A995BD /* sip-uac-info.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-info.c"; sourceTree = "<group>"; };
		461F0CCD232B40AD00A995BD /* sip-uac-transaction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "sip-uac-transaction.h"; sourceTree = "<group>"; };
		461F0CCE232B40AD00A995BD /* sip-uac-transaction-noninvite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-transaction-noninvite.c"; sourceTree = "<group>"; };
		461F0CCF232B40AD00A995BD /* sip-uac-subscribe.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-subscribe.c"; sourceTree = "<group>"; };
		461F0CD0232B40AD00A995BD /* sip-uac-transaction.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-transaction.c"; sourceTree = "<group>"; };
		461F0CD1232B40AE00A995BD /* sip-uac-cancel.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-cancel.c"; sourceTree = "<group>"; };
		461F0CD2232B40AE00A995BD /* sip-uac-register.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-register.c"; sourceTree = "<group>"; };
		461F0CD3232B40AE00A995BD /* sip-uac-invite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-invite.c"; sourceTree = "<group>"; };
		461F0CD4232B40AE00A995BD /* sip-uac-ack.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uac-ack.c"; sourceTree = "<group>"; };
		461F0CE3232B40BF00A995BD /* sip-uas-register.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-register.c"; sourceTree = "<group>"; };
		461F0CE4232B40BF00A995BD /* sip-uas-transaction-noninvite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-transaction-noninvite.c"; sourceTree = "<group>"; };
		461F0CE5232B40BF00A995BD /* sip-uas-transaction.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-transaction.c"; sourceTree = "<group>"; };
		461F0CE6232B40BF00A995BD /* sip-uas-transaction-invite.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-transaction-invite.c"; sourceTree = "<group>"; };
		461F0CE7232B40BF00A995BD /* sip-uas-prack.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-prack.c"; sourceTree = "<group>"; };
		461F0CE8232B40BF00A995BD /* sip-uas-options.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-options.c"; sourceTree = "<group>"; };
		461F0CE9232B40BF00A995BD /* sip-uas-transaction.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "sip-uas-transaction.h"; sourceTree = "<group>"; };
		461F0CEA232B40BF00A995BD /* sip-uas-cancel.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-cancel.c"; sourceTree = "<group>"; };
		461F0CEB232B40BF00A995BD /* sip-uas.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas.c"; sourceTree = "<group>"; };
		461F0CEC232B40BF00A995BD /* sip-uas-subscribe.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-subscribe.c"; sourceTree = "<group>"; };
		461F0CED232B40BF00A995BD /* sip-uas-bye.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-bye.c"; sourceTree = "<group>"; };
		461F0CEE232B40BF00A995BD /* sip-uas-refer.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-refer.c"; sourceTree = "<group>"; };
		461F0CEF232B40BF00A995BD /* sip-uas-info.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-uas-info.c"; sourceTree = "<group>"; };
		461F0CFD232B40F600A995BD /* sip-internal.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = "sip-internal.h"; sourceTree = "<group>"; };
		46A0D4F7229790E10070E1F5 /* sip-agent.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-agent.c"; sourceTree = "<group>"; };
		46C5B2A22183ED2700419E57 /* libsip.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libsip.a; sourceTree = BUILT_PRODUCTS_DIR; };
		46C5B42F2183EE6700419E57 /* include */ = {isa = PBXFileReference; lastKnownFileType = folder; path = include; sourceTree = "<group>"; };
		46C5B4472183EE7000419E57 /* sip-reason.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-reason.c"; sourceTree = "<group>"; };
		46C5B4482183EE7000419E57 /* sip-message.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-message.c"; sourceTree = "<group>"; };
		46C5B4492183EE7000419E57 /* sip-dialog.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-dialog.c"; sourceTree = "<group>"; };
		46C5B44B2183EE7000419E57 /* sip-header-contact.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-contact.c"; sourceTree = "<group>"; };
		46C5B44C2183EE7000419E57 /* sip-header-param.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-param.c"; sourceTree = "<group>"; };
		46C5B44D2183EE7000419E57 /* sip-header-cseq.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-cseq.c"; sourceTree = "<group>"; };
		46C5B44E2183EE7000419E57 /* sip-header-uri.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-uri.c"; sourceTree = "<group>"; };
		46C5B44F2183EE7000419E57 /* sip-header-via.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header-via.c"; sourceTree = "<group>"; };
		46C5B4502183EE7000419E57 /* sip-header.c */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.c; path = "sip-header.c"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		46C5B2A02183ED2700419E57 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		46C5B2992183ED2700419E57 = {
			isa = PBXGroup;
			children = (
				46C5B42F2183EE6700419E57 /* include */,
				46C5B2A32183ED2700419E57 /* Products */,
				46C5B4302183EE7000419E57 /* src */,
			);
			sourceTree = "<group>";
		};
		46C5B2A32183ED2700419E57 /* Products */ = {
			isa = PBXGroup;
			children = (
				46C5B2A22183ED2700419E57 /* libsip.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		46C5B4302183EE7000419E57 /* src */ = {
			isa = PBXGroup;
			children = (
				46C5B4312183EE7000419E57 /* uas */,
				46C5B43B2183EE7000419E57 /* uac */,
				46C5B4472183EE7000419E57 /* sip-reason.c */,
				461F0CFD232B40F600A995BD /* sip-internal.h */,
				46C5B4482183EE7000419E57 /* sip-message.c */,
				46C5B4492183EE7000419E57 /* sip-dialog.c */,
				46A0D4F7229790E10070E1F5 /* sip-agent.c */,
				461F0CA5232B404400A995BD /* sip-subscribe.c */,
				46C5B44A2183EE7000419E57 /* header */,
			);
			path = src;
			sourceTree = "<group>";
		};
		46C5B4312183EE7000419E57 /* uas */ = {
			isa = PBXGroup;
			children = (
				461F0CED232B40BF00A995BD /* sip-uas-bye.c */,
				461F0CEA232B40BF00A995BD /* sip-uas-cancel.c */,
				461F0CEF232B40BF00A995BD /* sip-uas-info.c */,
				461F0CE8232B40BF00A995BD /* sip-uas-options.c */,
				461F0CE7232B40BF00A995BD /* sip-uas-prack.c */,
				461F0CEE232B40BF00A995BD /* sip-uas-refer.c */,
				461F0CE3232B40BF00A995BD /* sip-uas-register.c */,
				461F0CEC232B40BF00A995BD /* sip-uas-subscribe.c */,
				461F0CE6232B40BF00A995BD /* sip-uas-transaction-invite.c */,
				461F0CE4232B40BF00A995BD /* sip-uas-transaction-noninvite.c */,
				461F0CE5232B40BF00A995BD /* sip-uas-transaction.c */,
				461F0CE9232B40BF00A995BD /* sip-uas-transaction.h */,
				461F0CEB232B40BF00A995BD /* sip-uas.c */,
			);
			path = uas;
			sourceTree = "<group>";
		};
		46C5B43B2183EE7000419E57 /* uac */ = {
			isa = PBXGroup;
			children = (
				461F0CD4232B40AE00A995BD /* sip-uac-ack.c */,
				461F0CC9232B40AD00A995BD /* sip-uac-bye.c */,
				461F0CD1232B40AE00A995BD /* sip-uac-cancel.c */,
				461F0CCB232B40AD00A995BD /* sip-uac-info.c */,
				461F0CD3232B40AE00A995BD /* sip-uac-invite.c */,
				461F0CC8232B40AD00A995BD /* sip-uac-options.c */,
				461F0CD2232B40AE00A995BD /* sip-uac-register.c */,
				461F0CCF232B40AD00A995BD /* sip-uac-subscribe.c */,
				461F0CCA232B40AD00A995BD /* sip-uac-transaction-invite.c */,
				461F0CCE232B40AD00A995BD /* sip-uac-transaction-noninvite.c */,
				461F0CD0232B40AD00A995BD /* sip-uac-transaction.c */,
				461F0CCD232B40AD00A995BD /* sip-uac-transaction.h */,
				461F0CC7232B40AD00A995BD /* sip-uac.c */,
			);
			path = uac;
			sourceTree = "<group>";
		};
		46C5B44A2183EE7000419E57 /* header */ = {
			isa = PBXGroup;
			children = (
				46C5B44B2183EE7000419E57 /* sip-header-contact.c */,
				461F0CA7232B406400A995BD /* sip-header-substate.c */,
				46C5B44C2183EE7000419E57 /* sip-header-param.c */,
				46C5B44D2183EE7000419E57 /* sip-header-cseq.c */,
				46C5B44E2183EE7000419E57 /* sip-header-uri.c */,
				46C5B44F2183EE7000419E57 /* sip-header-via.c */,
				46C5B4502183EE7000419E57 /* sip-header.c */,
			);
			path = header;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		46C5B29E2183ED2700419E57 /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
				461F0CF6232B40BF00A995BD /* sip-uas-transaction.h in Headers */,
				461F0CFE232B40F600A995BD /* sip-internal.h in Headers */,
				461F0CDB232B40AE00A995BD /* sip-uac-transaction.h in Headers */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		46C5B2A12183ED2700419E57 /* libsip */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46C5B2A62183ED2700419E57 /* Build configuration list for PBXNativeTarget "libsip" */;
			buildPhases = (
				46C5B29E2183ED2700419E57 /* Headers */,
				46C5B29F2183ED2700419E57 /* Sources */,
				46C5B2A02183ED2700419E57 /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = libsip;
			productName = libsip;
			productReference = 46C5B2A22183ED2700419E57 /* libsip.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		46C5B29A2183ED2700419E57 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1240;
				ORGANIZATIONNAME = ireader;
				TargetAttributes = {
					46C5B2A12183ED2700419E57 = {
						CreatedOnToolsVersion = 10.0;
					};
				};
			};
			buildConfigurationList = 46C5B29D2183ED2700419E57 /* Build configuration list for PBXProject "libsip" */;
			compatibilityVersion = "Xcode 9.3";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 46C5B2992183ED2700419E57;
			productRefGroup = 46C5B2A32183ED2700419E57 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				46C5B2A12183ED2700419E57 /* libsip */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		46C5B29F2183ED2700419E57 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				461F0CF3232B40BF00A995BD /* sip-uas-transaction-invite.c in Sources */,
				46C5B46B2183EE7000419E57 /* sip-header-uri.c in Sources */,
				461F0CD6232B40AE00A995BD /* sip-uac-options.c in Sources */,
				461F0CF1232B40BF00A995BD /* sip-uas-transaction-noninvite.c in Sources */,
				46A0D4F8229790E10070E1F5 /* sip-agent.c in Sources */,
				461F0CE2232B40AE00A995BD /* sip-uac-ack.c in Sources */,
				461F0CD9232B40AE00A995BD /* sip-uac-info.c in Sources */,
				461F0CFB232B40BF00A995BD /* sip-uas-refer.c in Sources */,
				461F0CDE232B40AE00A995BD /* sip-uac-transaction.c in Sources */,
				461F0CE0232B40AE00A995BD /* sip-uac-register.c in Sources */,
				461F0CFA232B40BF00A995BD /* sip-uas-bye.c in Sources */,
				461F0CD7232B40AE00A995BD /* sip-uac-bye.c in Sources */,
				461F0CD5232B40AE00A995BD /* sip-uac.c in Sources */,
				46C5B4682183EE7000419E57 /* sip-header-contact.c in Sources */,
				46C5B4662183EE7000419E57 /* sip-message.c in Sources */,
				46C5B4692183EE7000419E57 /* sip-header-param.c in Sources */,
				461F0CA6232B404400A995BD /* sip-subscribe.c in Sources */,
				461F0CF9232B40BF00A995BD /* sip-uas-subscribe.c in Sources */,
				461F0CF2232B40BF00A995BD /* sip-uas-transaction.c in Sources */,
				461F0CF5232B40BF00A995BD /* sip-uas-options.c in Sources */,
				461F0CA8232B406400A995BD /* sip-header-substate.c in Sources */,
				461F0CFC232B40BF00A995BD /* sip-uas-info.c in Sources */,
				46C5B4672183EE7000419E57 /* sip-dialog.c in Sources */,
				461F0CDC232B40AE00A995BD /* sip-uac-transaction-noninvite.c in Sources */,
				461F0CF4232B40BF00A995BD /* sip-uas-prack.c in Sources */,
				461F0CF7232B40BF00A995BD /* sip-uas-cancel.c in Sources */,
				461F0CD8232B40AE00A995BD /* sip-uac-transaction-invite.c in Sources */,
				46C5B46C2183EE7000419E57 /* sip-header-via.c in Sources */,
				461F0CF8232B40BF00A995BD /* sip-uas.c in Sources */,
				46C5B4652183EE7000419E57 /* sip-reason.c in Sources */,
				46C5B46A2183EE7000419E57 /* sip-header-cseq.c in Sources */,
				461F0CE1232B40AE00A995BD /* sip-uac-invite.c in Sources */,
				461F0CDD232B40AE00A995BD /* sip-uac-subscribe.c in Sources */,
				461F0CF0232B40BF00A995BD /* sip-uas-register.c in Sources */,
				461F0CDF232B40AE00A995BD /* sip-uac-cancel.c in Sources */,
				46C5B46D2183EE7000419E57 /* sip-header.c in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		46C5B2A42183ED2700419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
					OS_MAC,
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../../sdk/include,
					../../sdk/libhttp/include,
				);
			};
			name = Debug;
		};
		46C5B2A52183ED2700419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "-";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_PREPROCESSOR_DEFINITIONS = OS_MAC;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 10.13;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
				USER_HEADER_SEARCH_PATHS = (
					.,
					./include,
					../../sdk/include,
					../../sdk/libhttp/include,
				);
			};
			name = Release;
		};
		46C5B2A72183ED2700419E57 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Debug;
		};
		46C5B2A82183ED2700419E57 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				EXECUTABLE_PREFIX = "";
				GCC_PREPROCESSOR_DEFINITIONS = "$(inherited)";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				USER_HEADER_SEARCH_PATHS = "$(inherited)";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		46C5B29D2183ED2700419E57 /* Build configuration list for PBXProject "libsip" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B2A42183ED2700419E57 /* Debug */,
				46C5B2A52183ED2700419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46C5B2A62183ED2700419E57 /* Build configuration list for PBXNativeTarget "libsip" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46C5B2A72183ED2700419E57 /* Debug */,
				46C5B2A82183ED2700419E57 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 46C5B29A2183ED2700419E57 /* Project object */;
}
