#ifndef HLS_SERVER_H
#define HLS_SERVER_H

#include <memory>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>
#include <list>
#include "MediaSourceManager.h"
#include "http-server.h"
#include "hls-m3u8.h"
#include "hls-media.h"
#include "mpeg-ts.h"

// HLS片段
struct HlsSegment {
    std::string filename;
    double duration;
    uint64_t timestamp;
    std::vector<uint8_t> data;
    std::atomic<int> refcount{0};
};

// HLS播放列表
class HlsPlaylist : public MediaSession, public std::enable_shared_from_this<HlsPlaylist> {
public:
    HlsPlaylist(const std::string& path, std::shared_ptr<MediaSourceManager> sourceManager);
    ~HlsPlaylist();
    
    void Start();
    void Stop();
    
    // MediaSession接口
    void OnMediaPacket(const MediaPacket& packet) override;
    void OnMediaInfo(const MediaInfo& info) override;
    
    // 获取M3U8播放列表
    std::string GetM3U8() const;
    
    // 获取TS片段
    std::shared_ptr<HlsSegment> GetSegment(const std::string& filename);
    
private:
    std::string m_path;
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::shared_ptr<MediaSource> m_mediaSource;
    std::atomic<bool> m_running{false};
    
    // HLS相关
    hls_m3u8_t* m_m3u8;
    hls_media_t* m_hls;
    MediaInfo m_mediaInfo;
    
    // 片段管理
    mutable std::mutex m_segmentMutex;
    std::list<std::shared_ptr<HlsSegment>> m_segments;
    int m_segmentIndex = 0;
    
    // 配置
    static const int SEGMENT_DURATION = 10; // 秒
    static const int SEGMENT_COUNT = 5;     // 保留片段数
    
    // HLS回调
    static int OnHlsSegment(void* param, const void* data, size_t bytes, 
                           int64_t pts, int64_t dts, int64_t duration);
    
    void InitHLS();
    void CleanupSegments();
};

// HLS服务器
class HlsServer {
public:
    HlsServer(std::shared_ptr<MediaSourceManager> sourceManager);
    ~HlsServer();
    
    bool Start(int port, const std::string& path);
    void Stop();
    
private:
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::atomic<bool> m_running{false};
    http_server_t* m_httpServer;
    std::string m_path;
    
    // 播放列表管理
    mutable std::mutex m_playlistMutex;
    std::map<std::string, std::shared_ptr<HlsPlaylist>> m_playlists;
    
    // HTTP请求处理
    static int OnHttpRequest(void* param, http_session_t* session, 
                            const char* method, const char* uri);
    
    int HandleM3U8Request(http_session_t* session, const std::string& path);
    int HandleTSRequest(http_session_t* session, const std::string& path, const std::string& filename);
    
    std::shared_ptr<HlsPlaylist> GetOrCreatePlaylist(const std::string& path);
};

#endif // HLS_SERVER_H