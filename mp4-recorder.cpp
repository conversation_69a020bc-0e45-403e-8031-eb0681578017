#include "mp4-recorder.h"
#include <iostream>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <filesystem>
#include <openssl/md5.h>
#include <openssl/sha.h>
#include <json/json.h>

extern "C" {
#include "mov-buffer.h"
}

MP4Recorder::MP4Recorder(const RecordingConfig& config)
    : m_config(config)
    , m_mp4_writer(nullptr)
    , m_output_file(nullptr)
    , m_video_track(-1)
    , m_audio_track(-1)
    , m_video_track_added(false)
    , m_audio_track_added(false)
    , m_bytes_written(0)
    , m_video_params_set(false)
    , m_audio_params_set(false)
{
    // 创建输出目录
    std::filesystem::create_directories(m_config.output_dir);
    
    // 设置数据库文件路径
    m_db_file = m_config.output_dir + "/recordings.json";
    
    // 加载录像数据库
    LoadRecordingDatabase();
}

MP4Recorder::~MP4Recorder() {
    StopRecording();
}

bool MP4Recorder::StartRecording(const std::string& session_id) {
    if (m_state != RecordingState::STOPPED) {
        return false;
    }
    
    m_state = RecordingState::STARTING;
    m_current_session_id = session_id;
    
    // 创建新文件
    if (!CreateNewFile()) {
        m_state = RecordingState::ERROR;
        if (m_error_callback) {
            m_error_callback("Failed to create recording file");
        }
        return false;
    }
    
    // 启动工作线程
    m_running = true;
    m_worker_thread = std::thread(&MP4Recorder::WorkerThread, this);
    
    m_state = RecordingState::RECORDING;
    m_recording_start_time = std::chrono::steady_clock::now();
    
    if (m_start_callback) {
        m_start_callback(m_current_recording.filename);
    }
    
    return true;
}

bool MP4Recorder::StopRecording() {
    if (m_state != RecordingState::RECORDING) {
        return false;
    }
    
    m_state = RecordingState::STOPPING;
    
    // 停止工作线程
    m_running = false;
    if (m_worker_thread.joinable()) {
        m_worker_thread.join();
    }
    
    // 关闭当前文件
    CloseCurrentFile();
    
    m_state = RecordingState::STOPPED;
    
    if (m_stop_callback) {
        m_stop_callback(m_current_recording);
    }
    
    return true;
}

bool MP4Recorder::WriteVideoFrame(const MediaFrame& frame) {
    if (m_state != RecordingState::RECORDING) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_frame_mutex);
    m_frame_queue.push(frame);
    return true;
}

bool MP4Recorder::WriteAudioFrame(const MediaFrame& frame) {
    if (m_state != RecordingState::RECORDING) {
        return false;
    }
    
    std::lock_guard<std::mutex> lock(m_frame_mutex);
    m_frame_queue.push(frame);
    return true;
}

bool MP4Recorder::SetVideoParams(int width, int height, int fps,
                                 const std::vector<uint8_t>& sps,
                                 const std::vector<uint8_t>& pps) {
    m_config.video_width = width;
    m_config.video_height = height;
    m_config.video_fps = fps;

    if (!sps.empty() && !pps.empty()) {
        m_video_sps = sps;
        m_video_pps = pps;

        // 构建H.264 AVCDecoderConfigurationRecord
        if (SetH264ExtraData(sps, pps)) {
            m_video_params_set = true;
            return true;
        }
    }

    return false;
}

bool MP4Recorder::SetAudioParams(int channels, int sample_rate, int bits_per_sample,
                                 const std::vector<uint8_t>& config) {
    m_config.audio_channels = channels;
    m_config.audio_sample_rate = sample_rate;
    m_config.audio_bits_per_sample = bits_per_sample;

    if (!config.empty()) {
        m_audio_config = config;
        m_audio_params_set = true;
        return true;
    } else {
        // 如果没有提供配置，尝试生成默认的AAC配置
        std::vector<uint8_t> generated_config;
        if (GenerateAACConfig(sample_rate, channels, generated_config)) {
            m_audio_config = generated_config;
            m_audio_params_set = true;
            return true;
        }
    }

    return false;
}

RecordingInfo MP4Recorder::GetCurrentRecordingInfo() const {
    return m_current_recording;
}

std::vector<RecordingInfo> MP4Recorder::GetAllRecordings() const {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    return m_recordings_db;
}

bool MP4Recorder::DeleteRecording(const std::string& hash) {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    
    auto it = std::find_if(m_recordings_db.begin(), m_recordings_db.end(),
        [&hash](const RecordingInfo& info) {
            return info.hash_md5 == hash || info.hash_sha256 == hash;
        });
    
    if (it != m_recordings_db.end()) {
        // 删除文件
        std::filesystem::remove(it->filepath);
        
        // 从数据库中移除
        m_recordings_db.erase(it);
        
        // 保存数据库
        SaveRecordingInfo(*it);
        
        return true;
    }
    
    return false;
}

bool MP4Recorder::FindRecordingByHash(const std::string& hash, RecordingInfo& info) const {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    
    auto it = std::find_if(m_recordings_db.begin(), m_recordings_db.end(),
        [&hash](const RecordingInfo& recording) {
            return recording.hash_md5 == hash || recording.hash_sha256 == hash;
        });
    
    if (it != m_recordings_db.end()) {
        info = *it;
        return true;
    }
    
    return false;
}

std::vector<RecordingInfo> MP4Recorder::SearchRecordingsByTag(const std::string& tag, 
                                                              const std::string& value) const {
    std::lock_guard<std::mutex> lock(m_db_mutex);
    std::vector<RecordingInfo> results;
    
    for (const auto& recording : m_recordings_db) {
        auto it = recording.tags.find(tag);
        if (it != recording.tags.end() && it->second == value) {
            results.push_back(recording);
        }
    }
    
    return results;
}

bool MP4Recorder::AddTag(const std::string& key, const std::string& value) {
    m_current_tags[key] = value;
    return true;
}

bool MP4Recorder::RemoveTag(const std::string& key) {
    m_current_tags.erase(key);
    return true;
}

// MOV buffer callbacks
int MP4Recorder::mov_buffer_read(void* param, void* data, uint64_t bytes) {
    FILE* fp = (FILE*)param;
    return (int)fread(data, 1, bytes, fp);
}

int MP4Recorder::mov_buffer_write(void* param, const void* data, uint64_t bytes) {
    FILE* fp = (FILE*)param;
    return (int)fwrite(data, 1, bytes, fp);
}

int MP4Recorder::mov_buffer_seek(void* param, int64_t offset) {
    FILE* fp = (FILE*)param;
    return fseek(fp, offset, SEEK_SET);
}

int64_t MP4Recorder::mov_buffer_tell(void* param) {
    FILE* fp = (FILE*)param;
    return ftell(fp);
}

void MP4Recorder::WorkerThread() {
    while (m_running || !m_frame_queue.empty()) {
        MediaFrame frame;
        bool has_frame = false;

        // 获取帧数据
        {
            std::lock_guard<std::mutex> lock(m_frame_mutex);
            if (!m_frame_queue.empty()) {
                frame = m_frame_queue.front();
                m_frame_queue.pop();
                has_frame = true;
            }
        }

        if (has_frame) {
            ProcessFrame(frame);

            // 检查文件大小和时长限制
            if (CheckFileLimits()) {
                // 需要创建新文件
                CloseCurrentFile();
                CreateNewFile();
            }
        } else {
            std::this_thread::sleep_for(std::chrono::milliseconds(10));
        }
    }
}

bool MP4Recorder::CreateNewFile() {
    // 生成文件名
    std::string filename = GenerateFilename();
    std::string filepath = m_config.output_dir + "/" + filename;

    // 打开文件
    m_output_file = fopen(filepath.c_str(), "wb");
    if (!m_output_file) {
        return false;
    }

    // 创建MP4写入器
    struct mov_buffer_t buffer = {
        mov_buffer_read,
        mov_buffer_write,
        mov_buffer_seek,
        mov_buffer_tell
    };

    m_mp4_writer = mp4_writer_create(0, &buffer, m_output_file, MOV_FLAG_FASTSTART);
    if (!m_mp4_writer) {
        fclose(m_output_file);
        m_output_file = nullptr;
        return false;
    }

    // 重置轨道状态
    m_video_track = -1;
    m_audio_track = -1;
    m_video_track_added = false;
    m_audio_track_added = false;
    m_bytes_written = 0;

    // 设置当前录像信息
    m_current_recording.filename = filename;
    m_current_recording.filepath = filepath;
    m_current_recording.start_time = std::chrono::system_clock::now();
    m_current_recording.tags = m_current_tags;

    return true;
}

bool MP4Recorder::CloseCurrentFile() {
    if (m_mp4_writer) {
        mp4_writer_destroy(m_mp4_writer);
        m_mp4_writer = nullptr;
    }

    if (m_output_file) {
        fclose(m_output_file);
        m_output_file = nullptr;
    }

    // 更新录像信息
    m_current_recording.end_time = std::chrono::system_clock::now();
    m_current_recording.file_size = std::filesystem::file_size(m_current_recording.filepath);

    auto duration = std::chrono::duration_cast<std::chrono::seconds>(
        m_current_recording.end_time - m_current_recording.start_time);
    m_current_recording.duration = duration.count();

    // 计算文件hash
    if (m_config.enable_hash_tagging) {
        CalculateFileHash();
    }

    // 保存录像信息到数据库
    {
        std::lock_guard<std::mutex> lock(m_db_mutex);
        m_recordings_db.push_back(m_current_recording);
        SaveRecordingInfo(m_current_recording);
    }

    return true;
}

bool MP4Recorder::ProcessFrame(const MediaFrame& frame) {
    if (!m_mp4_writer) {
        return false;
    }

    // 添加视频轨道
    if (frame.type == MediaFrame::VIDEO_H264 && !m_video_track_added) {
        if (m_video_params_set && !m_h264_extra_data.empty()) {
            m_video_track = mp4_writer_add_video(m_mp4_writer, MOV_OBJECT_H264,
                                               m_config.video_width, m_config.video_height,
                                               m_h264_extra_data.data(), m_h264_extra_data.size());
            if (m_video_track >= 0) {
                m_video_track_added = true;
            }
        } else {
            // 尝试从帧数据中提取SPS/PPS
            std::vector<uint8_t> sps, pps;
            if (ExtractH264ParamsFromFrame(frame.data, sps, pps)) {
                if (SetH264ExtraData(sps, pps)) {
                    m_video_track = mp4_writer_add_video(m_mp4_writer, MOV_OBJECT_H264,
                                                       m_config.video_width, m_config.video_height,
                                                       m_h264_extra_data.data(), m_h264_extra_data.size());
                    if (m_video_track >= 0) {
                        m_video_track_added = true;
                        m_video_params_set = true;
                    }
                }
            }
        }
    }

    // 添加音频轨道
    if (frame.type == MediaFrame::AUDIO_AAC && !m_audio_track_added) {
        if (m_audio_params_set && !m_audio_config.empty()) {
            m_audio_track = mp4_writer_add_audio(m_mp4_writer, MOV_OBJECT_AAC,
                                               m_config.audio_channels,
                                               m_config.audio_bits_per_sample,
                                               m_config.audio_sample_rate,
                                               m_audio_config.data(), m_audio_config.size());
            if (m_audio_track >= 0) {
                m_audio_track_added = true;
            }
        } else {
            // 生成默认的AAC配置
            std::vector<uint8_t> default_config;
            if (GenerateAACConfig(m_config.audio_sample_rate, m_config.audio_channels, default_config)) {
                m_audio_config = default_config;
                m_audio_track = mp4_writer_add_audio(m_mp4_writer, MOV_OBJECT_AAC,
                                                   m_config.audio_channels,
                                                   m_config.audio_bits_per_sample,
                                                   m_config.audio_sample_rate,
                                                   m_audio_config.data(), m_audio_config.size());
                if (m_audio_track >= 0) {
                    m_audio_track_added = true;
                    m_audio_params_set = true;
                }
            }
        }
    }

    // 写入帧数据
    int track = -1;
    std::vector<uint8_t> processed_data;

    if (frame.type == MediaFrame::VIDEO_H264 && m_video_track_added) {
        track = m_video_track;
        // H.264帧需要转换为AVCC格式（NALU长度前缀）
        if (ConvertAnnexBToAVCC(frame.data, processed_data)) {
            // 使用转换后的数据
        } else {
            // 如果转换失败，假设已经是AVCC格式
            processed_data = frame.data;
        }
    } else if (frame.type == MediaFrame::AUDIO_AAC && m_audio_track_added) {
        track = m_audio_track;
        // AAC帧需要去除ADTS头（如果有的话）
        if (frame.data.size() >= 7 && frame.data[0] == 0xFF && (frame.data[1] & 0xF0) == 0xF0) {
            // 有ADTS头，需要去除
            uint16_t frame_length = ((frame.data[3] & 0x03) << 11) |
                                   (frame.data[4] << 3) |
                                   ((frame.data[5] >> 5) & 0x07);
            uint8_t adts_header_length = (frame.data[1] & 0x01) ? 7 : 9;

            if (frame_length > adts_header_length && frame_length <= frame.data.size()) {
                processed_data.assign(frame.data.begin() + adts_header_length,
                                    frame.data.begin() + frame_length);
            } else {
                processed_data = frame.data;
            }
        } else {
            // 没有ADTS头，直接使用原始数据
            processed_data = frame.data;
        }
    }

    if (track >= 0 && !processed_data.empty()) {
        int flags = frame.keyframe ? MOV_AV_FLAG_KEYFREAME : 0;
        int ret = mp4_writer_write(m_mp4_writer, track, processed_data.data(), processed_data.size(),
                                 frame.pts, frame.dts, flags);
        if (ret == 0) {
            m_bytes_written += processed_data.size();
            return true;
        }
    }

    return false;
}

bool MP4Recorder::CheckFileLimits() {
    // 检查文件大小限制
    if (m_bytes_written >= m_config.max_file_size) {
        return true;
    }

    // 检查录像时长限制
    auto now = std::chrono::steady_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(now - m_recording_start_time);
    if (duration.count() >= m_config.max_duration) {
        return true;
    }

    return false;
}

void MP4Recorder::CalculateFileHash() {
    if (m_current_recording.filepath.empty()) {
        return;
    }

    std::ifstream file(m_current_recording.filepath, std::ios::binary);
    if (!file.is_open()) {
        return;
    }

    // 计算MD5
    MD5_CTX md5_ctx;
    MD5_Init(&md5_ctx);

    // 计算SHA256
    SHA256_CTX sha256_ctx;
    SHA256_Init(&sha256_ctx);

    char buffer[8192];
    while (file.read(buffer, sizeof(buffer)) || file.gcount() > 0) {
        size_t bytes_read = file.gcount();
        MD5_Update(&md5_ctx, buffer, bytes_read);
        SHA256_Update(&sha256_ctx, buffer, bytes_read);
    }

    // 获取MD5结果
    unsigned char md5_digest[MD5_DIGEST_LENGTH];
    MD5_Final(md5_digest, &md5_ctx);

    std::stringstream md5_ss;
    for (int i = 0; i < MD5_DIGEST_LENGTH; i++) {
        md5_ss << std::hex << std::setw(2) << std::setfill('0') << (int)md5_digest[i];
    }
    m_current_recording.hash_md5 = md5_ss.str();

    // 获取SHA256结果
    unsigned char sha256_digest[SHA256_DIGEST_LENGTH];
    SHA256_Final(sha256_digest, &sha256_ctx);

    std::stringstream sha256_ss;
    for (int i = 0; i < SHA256_DIGEST_LENGTH; i++) {
        sha256_ss << std::hex << std::setw(2) << std::setfill('0') << (int)sha256_digest[i];
    }
    m_current_recording.hash_sha256 = sha256_ss.str();
}

std::string MP4Recorder::GenerateFilename() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << m_config.file_prefix << "_"
       << std::put_time(std::localtime(&time_t), "%Y%m%d_%H%M%S")
       << "_" << std::setfill('0') << std::setw(3) << ms.count();

    if (!m_current_session_id.empty()) {
        ss << "_" << m_current_session_id;
    }

    ss << ".mp4";
    return ss.str();
}

bool MP4Recorder::SaveRecordingInfo(const RecordingInfo& info) {
    Json::Value root;
    Json::Value recordings(Json::arrayValue);

    // 加载现有数据
    std::ifstream file(m_db_file);
    if (file.is_open()) {
        file >> root;
        if (root.isMember("recordings")) {
            recordings = root["recordings"];
        }
    }

    // 添加新记录
    Json::Value record;
    record["filename"] = info.filename;
    record["filepath"] = info.filepath;
    record["hash_md5"] = info.hash_md5;
    record["hash_sha256"] = info.hash_sha256;
    record["file_size"] = (Json::UInt64)info.file_size;
    record["duration"] = info.duration;

    auto start_time_t = std::chrono::system_clock::to_time_t(info.start_time);
    auto end_time_t = std::chrono::system_clock::to_time_t(info.end_time);
    record["start_time"] = (Json::UInt64)start_time_t;
    record["end_time"] = (Json::UInt64)end_time_t;

    Json::Value tags;
    for (const auto& tag : info.tags) {
        tags[tag.first] = tag.second;
    }
    record["tags"] = tags;

    recordings.append(record);
    root["recordings"] = recordings;

    // 保存到文件
    std::ofstream out_file(m_db_file);
    if (out_file.is_open()) {
        out_file << root;
        return true;
    }

    return false;
}

bool MP4Recorder::LoadRecordingDatabase() {
    std::ifstream file(m_db_file);
    if (!file.is_open()) {
        return true; // 文件不存在是正常的
    }

    Json::Value root;
    file >> root;

    if (!root.isMember("recordings")) {
        return true;
    }

    const Json::Value& recordings = root["recordings"];
    for (const auto& record : recordings) {
        RecordingInfo info;
        info.filename = record["filename"].asString();
        info.filepath = record["filepath"].asString();
        info.hash_md5 = record["hash_md5"].asString();
        info.hash_sha256 = record["hash_sha256"].asString();
        info.file_size = record["file_size"].asUInt64();
        info.duration = record["duration"].asUInt();

        auto start_time_t = record["start_time"].asUInt64();
        auto end_time_t = record["end_time"].asUInt64();
        info.start_time = std::chrono::system_clock::from_time_t(start_time_t);
        info.end_time = std::chrono::system_clock::from_time_t(end_time_t);

        const Json::Value& tags = record["tags"];
        for (const auto& key : tags.getMemberNames()) {
            info.tags[key] = tags[key].asString();
        }

        m_recordings_db.push_back(info);
    }

    return true;
}

bool MP4Recorder::SetH264ExtraData(const std::vector<uint8_t>& sps, const std::vector<uint8_t>& pps) {
    if (sps.empty() || pps.empty()) {
        return false;
    }

    // 构建AVCDecoderConfigurationRecord
    // 格式: configurationVersion(1) + AVCProfileIndication(1) + profile_compatibility(1) +
    //       AVCLevelIndication(1) + lengthSizeMinusOne(1) + numOfSequenceParameterSets(1) +
    //       sequenceParameterSetLength(2) + sequenceParameterSetNALUnit +
    //       numOfPictureParameterSets(1) + pictureParameterSetLength(2) + pictureParameterSetNALUnit

    m_h264_extra_data.clear();

    // configurationVersion
    m_h264_extra_data.push_back(0x01);

    // 从SPS中提取profile, compatibility, level
    if (sps.size() >= 4) {
        m_h264_extra_data.push_back(sps[1]); // AVCProfileIndication
        m_h264_extra_data.push_back(sps[2]); // profile_compatibility
        m_h264_extra_data.push_back(sps[3]); // AVCLevelIndication
    } else {
        // 默认值
        m_h264_extra_data.push_back(0x42); // Baseline Profile
        m_h264_extra_data.push_back(0x00); // profile_compatibility
        m_h264_extra_data.push_back(0x1E); // Level 3.0
    }

    // lengthSizeMinusOne (NALU长度字段大小-1, 通常为3表示4字节)
    m_h264_extra_data.push_back(0xFF);

    // numOfSequenceParameterSets
    m_h264_extra_data.push_back(0xE1); // 0xE0 | 1

    // sequenceParameterSetLength
    uint16_t sps_length = sps.size();
    m_h264_extra_data.push_back((sps_length >> 8) & 0xFF);
    m_h264_extra_data.push_back(sps_length & 0xFF);

    // sequenceParameterSetNALUnit
    m_h264_extra_data.insert(m_h264_extra_data.end(), sps.begin(), sps.end());

    // numOfPictureParameterSets
    m_h264_extra_data.push_back(0x01);

    // pictureParameterSetLength
    uint16_t pps_length = pps.size();
    m_h264_extra_data.push_back((pps_length >> 8) & 0xFF);
    m_h264_extra_data.push_back(pps_length & 0xFF);

    // pictureParameterSetNALUnit
    m_h264_extra_data.insert(m_h264_extra_data.end(), pps.begin(), pps.end());

    return true;
}

bool MP4Recorder::ParseH264ExtraData(const uint8_t* data, size_t size,
                                     std::vector<uint8_t>& sps, std::vector<uint8_t>& pps) {
    if (!data || size < 7) {
        return false;
    }

    const uint8_t* ptr = data;
    const uint8_t* end = data + size;

    // 跳过configurationVersion, profile, compatibility, level, lengthSizeMinusOne
    ptr += 5;

    if (ptr >= end) return false;

    // numOfSequenceParameterSets
    uint8_t num_sps = *ptr++ & 0x1F;

    for (int i = 0; i < num_sps && ptr < end - 2; i++) {
        // sequenceParameterSetLength
        uint16_t sps_length = (ptr[0] << 8) | ptr[1];
        ptr += 2;

        if (ptr + sps_length > end) return false;

        // sequenceParameterSetNALUnit
        sps.assign(ptr, ptr + sps_length);
        ptr += sps_length;
        break; // 通常只有一个SPS
    }

    if (ptr >= end) return false;

    // numOfPictureParameterSets
    uint8_t num_pps = *ptr++;

    for (int i = 0; i < num_pps && ptr < end - 2; i++) {
        // pictureParameterSetLength
        uint16_t pps_length = (ptr[0] << 8) | ptr[1];
        ptr += 2;

        if (ptr + pps_length > end) return false;

        // pictureParameterSetNALUnit
        pps.assign(ptr, ptr + pps_length);
        ptr += pps_length;
        break; // 通常只有一个PPS
    }

    return !sps.empty() && !pps.empty();
}

bool MP4Recorder::SetAACConfig(const std::vector<uint8_t>& config) {
    if (config.empty()) {
        return false;
    }

    m_audio_config = config;
    m_audio_params_set = true;
    return true;
}

bool MP4Recorder::GenerateAACConfig(int sample_rate, int channels, std::vector<uint8_t>& config) {
    // AAC AudioSpecificConfig格式:
    // audioObjectType(5) + samplingFrequencyIndex(4) + channelConfiguration(4) + ...

    // 获取采样率索引
    int sample_rate_index = -1;
    int sample_rates[] = {96000, 88200, 64000, 48000, 44100, 32000, 24000, 22050,
                         16000, 12000, 11025, 8000, 7350};
    for (int i = 0; i < 13; i++) {
        if (sample_rates[i] == sample_rate) {
            sample_rate_index = i;
            break;
        }
    }

    if (sample_rate_index == -1) {
        return false; // 不支持的采样率
    }

    // 检查声道数
    if (channels < 1 || channels > 8) {
        return false;
    }

    config.clear();

    // audioObjectType = 2 (AAC LC)
    // samplingFrequencyIndex
    // channelConfiguration
    uint16_t aac_config = (2 << 11) | (sample_rate_index << 7) | (channels << 3);

    config.push_back((aac_config >> 8) & 0xFF);
    config.push_back(aac_config & 0xFF);

    return true;
}

bool MP4Recorder::ExtractH264ParamsFromFrame(const std::vector<uint8_t>& frame_data,
                                            std::vector<uint8_t>& sps, std::vector<uint8_t>& pps) {
    const uint8_t* data = frame_data.data();
    size_t size = frame_data.size();
    const uint8_t* ptr = data;
    const uint8_t* end = data + size;

    sps.clear();
    pps.clear();

    while (ptr < end - 4) {
        // 查找起始码 0x00000001 或 0x000001
        if (ptr[0] == 0x00 && ptr[1] == 0x00) {
            int start_code_len = 0;
            if (ptr[2] == 0x00 && ptr[3] == 0x01) {
                start_code_len = 4;
            } else if (ptr[2] == 0x01) {
                start_code_len = 3;
            } else {
                ptr++;
                continue;
            }

            ptr += start_code_len;
            if (ptr >= end) break;

            // 解析NALU头
            uint8_t nalu_type = ptr[0] & 0x1F;

            // 查找下一个起始码
            const uint8_t* next_start = ptr + 1;
            while (next_start < end - 3) {
                if (next_start[0] == 0x00 && next_start[1] == 0x00 &&
                    (next_start[2] == 0x01 || (next_start[2] == 0x00 && next_start[3] == 0x01))) {
                    break;
                }
                next_start++;
            }

            size_t nalu_size = next_start - ptr;

            // 提取SPS和PPS
            if (nalu_type == 7) { // SPS
                sps.assign(ptr, ptr + nalu_size);
            } else if (nalu_type == 8) { // PPS
                pps.assign(ptr, ptr + nalu_size);
            }

            ptr = next_start;
        } else {
            ptr++;
        }
    }

    return !sps.empty() && !pps.empty();
}

bool MP4Recorder::ConvertAnnexBToAVCC(const std::vector<uint8_t>& annexb_data, std::vector<uint8_t>& avcc_data) {
    const uint8_t* data = annexb_data.data();
    size_t size = annexb_data.size();
    const uint8_t* ptr = data;
    const uint8_t* end = data + size;

    avcc_data.clear();

    while (ptr < end - 4) {
        // 查找起始码
        if (ptr[0] == 0x00 && ptr[1] == 0x00) {
            int start_code_len = 0;
            if (ptr[2] == 0x00 && ptr[3] == 0x01) {
                start_code_len = 4;
            } else if (ptr[2] == 0x01) {
                start_code_len = 3;
            } else {
                ptr++;
                continue;
            }

            ptr += start_code_len;
            if (ptr >= end) break;

            // 查找下一个起始码
            const uint8_t* next_start = ptr;
            while (next_start < end - 3) {
                if (next_start[0] == 0x00 && next_start[1] == 0x00 &&
                    (next_start[2] == 0x01 || (next_start[2] == 0x00 && next_start[3] == 0x01))) {
                    break;
                }
                next_start++;
            }

            size_t nalu_size = next_start - ptr;

            // 写入NALU长度（4字节大端序）
            avcc_data.push_back((nalu_size >> 24) & 0xFF);
            avcc_data.push_back((nalu_size >> 16) & 0xFF);
            avcc_data.push_back((nalu_size >> 8) & 0xFF);
            avcc_data.push_back(nalu_size & 0xFF);

            // 写入NALU数据
            avcc_data.insert(avcc_data.end(), ptr, ptr + nalu_size);

            ptr = next_start;
        } else {
            ptr++;
        }
    }

    return !avcc_data.empty();
}

bool MP4Recorder::ExtractAACConfig(const std::vector<uint8_t>& frame_data, std::vector<uint8_t>& config) {
    // 如果帧数据包含ADTS头，提取AudioSpecificConfig
    if (frame_data.size() < 7) {
        return false;
    }

    const uint8_t* data = frame_data.data();

    // 检查ADTS同步字 0xFFF
    if ((data[0] != 0xFF) || ((data[1] & 0xF0) != 0xF0)) {
        return false;
    }

    // 提取ADTS头信息
    uint8_t profile = (data[2] >> 6) & 0x03;
    uint8_t sample_rate_index = (data[2] >> 2) & 0x0F;
    uint8_t channel_config = ((data[2] & 0x01) << 2) | ((data[3] >> 6) & 0x03);

    // 构建AudioSpecificConfig
    config.clear();

    // audioObjectType (profile + 1) + samplingFrequencyIndex + channelConfiguration
    uint16_t asc = ((profile + 1) << 11) | (sample_rate_index << 7) | (channel_config << 3);

    config.push_back((asc >> 8) & 0xFF);
    config.push_back(asc & 0xFF);

    return true;
}
