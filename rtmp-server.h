#ifndef RTMP_SERVER_H
#define RTMP_SERVER_H

#include <memory>
#include <thread>
#include <atomic>
#include <map>
#include <mutex>
#include "rtmp-server.h"
#include "sockutil.h"
#include "MediaSourceManager.h"

class RtmpSession;

class RtmpServer {
public:
    RtmpServer(std::shared_ptr<MediaSourceManager> sourceManager);
    ~RtmpServer();
    
    bool Start(int port);
    void Stop();
    
private:
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::atomic<bool> m_running{false};
    socket_t m_socket;
    std::thread m_acceptThread;
    
    std::mutex m_sessionMutex;
    std::map<void*, std::shared_ptr<RtmpSession>> m_sessions;
    
    void AcceptThread();
    void HandleClient(socket_t client);
    
    // RTMP回调
    static int OnSend(void* param, const void* header, size_t len, const void* data, size_t bytes);
    static int OnPlay(void* param, const char* app, const char* stream, double start, double duration, uint8_t reset);
    static int OnPublish(void* param, const char* app, const char* stream, const char* type);
    static int OnPause(void* param, int pause, uint32_t ms);
    static int OnSeek(void* param, uint32_t ms);
    static int OnVideo(void* param, const void* data, size_t bytes, uint32_t timestamp);
    static int OnAudio(void* param, const void* data, size_t bytes, uint32_t timestamp);
    static int OnScript(void* param, const void* data, size_t bytes, uint32_t timestamp);
    static int OnGetDuration(void* param, const char* app, const char* stream, double* duration);
};

// RTMP会话
class RtmpSession : public MediaSession, public std::enable_shared_from_this<RtmpSession> {
public:
    RtmpSession(socket_t socket, rtmp_server_t* rtmp, std::shared_ptr<MediaSourceManager> sourceManager);
    ~RtmpSession();
    
    void Start();
    void Stop();
    
    // MediaSession接口
    void OnMediaPacket(const MediaPacket& packet) override;
    void OnMediaInfo(const MediaInfo& info) override;
    
    // RTMP数据发送
    int Send(const void* header, size_t len, const void* data, size_t bytes);
    
    // 获取socket
    socket_t GetSocket() const { return m_socket; }
    
    // 设置媒体源
    void SetMediaSource(std::shared_ptr<MediaSource> source);
    
    // 处理网络数据
    void ProcessData();
    
    // RTMP服务器指针
    rtmp_server_t* GetRtmpServer() { return m_rtmp; }
    
    // 流信息
    std::string m_app;
    std::string m_stream;
    bool m_isPublisher = false;

private:
    socket_t m_socket;
    rtmp_server_t* m_rtmp;
    std::shared_ptr<MediaSourceManager> m_sourceManager;
    std::shared_ptr<MediaSource> m_mediaSource;
    std::atomic<bool> m_running{false};
    std::thread m_thread;
    
    MediaInfo m_mediaInfo;
    bool m_metadataSent = false;
    bool m_videoConfigSent = false;
    bool m_audioConfigSent = false;
    
    void NetworkThread();
    void SendMetadata();
    void SendVideoConfig();
    void SendAudioConfig();
};

#endif // RTMP_SERVER_H