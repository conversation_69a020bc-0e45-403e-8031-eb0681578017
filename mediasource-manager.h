#ifndef MEDIA_SOURCE_MANAGER_H
#define MEDIA_SOURCE_MANAGER_H

#include <string>
#include <memory>
#include <map>
#include <mutex>
#include <functional>
#include <vector>

// Forward declarations
class MediaSource;
class MediaSession;

// 媒体数据包
struct MediaPacket {
    enum Type {
        VIDEO_H264,
        VIDEO_H265,
        AUDIO_AAC,
        AUDIO_G711A,
        AUDIO_G711U,
        METADATA
    };
    
    Type type;
    uint32_t timestamp;
    std::vector<uint8_t> data;
    bool keyframe;
};

// 媒体流信息
struct MediaInfo {
    // 视频信息
    bool has_video = false;
    int video_codec = 0; // 0: none, 1: H264, 2: H265
    int video_width = 0;
    int video_height = 0;
    int video_fps = 0;
    std::vector<uint8_t> video_sps;
    std::vector<uint8_t> video_pps;
    
    // 音频信息
    bool has_audio = false;
    int audio_codec = 0; // 0: none, 1: AAC, 2: G711A, 3: G711U
    int audio_channels = 0;
    int audio_sample_rate = 0;
    int audio_bits_per_sample = 0;
    std::vector<uint8_t> audio_config;
};

// 媒体源接口
class MediaSource {
public:
    virtual ~MediaSource() = default;
    
    virtual const std::string& GetStreamPath() const = 0;
    virtual const MediaInfo& GetMediaInfo() const = 0;
    virtual bool IsLive() const = 0;
    
    // 添加数据接收器
    virtual void AddSink(std::weak_ptr<MediaSession> session) = 0;
    virtual void RemoveSink(std::weak_ptr<MediaSession> session) = 0;
    
    // 推送媒体数据
    virtual void PushPacket(const MediaPacket& packet) = 0;
};

// 媒体会话接口
class MediaSession {
public:
    virtual ~MediaSession() = default;
    
    virtual void OnMediaPacket(const MediaPacket& packet) = 0;
    virtual void OnMediaInfo(const MediaInfo& info) = 0;
};

// 媒体源管理器
class MediaSourceManager {
public:
    MediaSourceManager();
    ~MediaSourceManager();
    
    // 创建或获取媒体源
    std::shared_ptr<MediaSource> CreateOrGetSource(const std::string& path, bool isLive = true);
    
    // 移除媒体源
    void RemoveSource(const std::string& path);
    
    // 查找媒体源
    std::shared_ptr<MediaSource> FindSource(const std::string& path);
    
    // 处理媒体数据
    void ProcessMediaData();
    
    // 从文件创建媒体源
    std::shared_ptr<MediaSource> CreateFileSource(const std::string& path, const std::string& filePath);

private:
    std::mutex m_mutex;
    std::map<std::string, std::shared_ptr<MediaSource>> m_sources;
};

#endif // MEDIA_SOURCE_MANAGER_H